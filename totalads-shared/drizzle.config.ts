import { defineConfig } from 'drizzle-kit';

import loadEnv from './src/config/loadEnv';

loadEnv();

// Check if we're using SQLite or PostgreSQL
const isUsingSQLite = process.env.DB_URL?.startsWith("file:");

export default defineConfig({
	schema: isUsingSQLite
		? "./src/models/sqliteDb/*"
		: "./src/models/postgresDb/*",
	out: "./drizzle-migrations",
	dialect: isUsingSQLite ? "sqlite" : "postgresql",
	dbCredentials: isUsingSQLite
		? { url: process.env.DB_URL!.replace("file:", "") }
		: {
				url: process.env.DB_URL as string,
				ssl: true,
		  },
	verbose: true,
	strict: true,
});
