CREATE TABLE `api_tokens` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`token` text NOT NULL,
	`name` text NOT NULL,
	`active` integer DEFAULT true NOT NULL,
	`created_at` integer NOT NULL,
	`last_used_at` integer,
	`revoked_at` integer,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `idx_api_tokens_token` ON `api_tokens` (`token`);--> statement-breakpoint
CREATE UNIQUE INDEX `idx_api_tokens_user_id` ON `api_tokens` (`user_id`);--> statement-breakpoint
CREATE TABLE `api_usage` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`token_id` integer,
	`endpoint` text NOT NULL,
	`request_id` text NOT NULL,
	`timestamp` integer NOT NULL,
	`response_status` integer,
	`charged` integer DEFAULT false NOT NULL,
	`billable` integer DEFAULT true NOT NULL,
	`month` text NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`token_id`) REFERENCES `api_tokens`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `user_email_verification_codes` (
	`user_id` integer NOT NULL,
	`verification_code` text NOT NULL,
	`created_at` integer NOT NULL,
	`expires_at` integer NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `idx_user_email_verification_codes_user_id` ON `user_email_verification_codes` (`user_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `idx_user_email_verification_codes_verification_code` ON `user_email_verification_codes` (`verification_code`);--> statement-breakpoint
CREATE TABLE `user_reset_password_codes` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` integer NOT NULL,
	`code` text NOT NULL,
	`expires_at` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	`used_at` integer,
	`attempts` integer DEFAULT 0,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text,
	`email` text NOT NULL,
	`account_created_at` integer NOT NULL,
	`password` text NOT NULL,
	`email_verified` integer DEFAULT false NOT NULL,
	`user_type` text DEFAULT 'regular' NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `idx_users_email` ON `users` (`email`);