{"version": "6", "dialect": "sqlite", "id": "04390491-002a-4aa3-9f39-225dd77e82f1", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"api_tokens": {"name": "api_tokens", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_used_at": {"name": "last_used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "revoked_at": {"name": "revoked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_api_tokens_token": {"name": "idx_api_tokens_token", "columns": ["token"], "isUnique": true}, "idx_api_tokens_user_id": {"name": "idx_api_tokens_user_id", "columns": ["user_id"], "isUnique": true}}, "foreignKeys": {"api_tokens_user_id_users_id_fk": {"name": "api_tokens_user_id_users_id_fk", "tableFrom": "api_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "api_usage": {"name": "api_usage", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token_id": {"name": "token_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "endpoint": {"name": "endpoint", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "response_status": {"name": "response_status", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "charged": {"name": "charged", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "billable": {"name": "billable", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "month": {"name": "month", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"api_usage_user_id_users_id_fk": {"name": "api_usage_user_id_users_id_fk", "tableFrom": "api_usage", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "api_usage_token_id_api_tokens_id_fk": {"name": "api_usage_token_id_api_tokens_id_fk", "tableFrom": "api_usage", "tableTo": "api_tokens", "columnsFrom": ["token_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_email_verification_codes": {"name": "user_email_verification_codes", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "verification_code": {"name": "verification_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_user_email_verification_codes_user_id": {"name": "idx_user_email_verification_codes_user_id", "columns": ["user_id"], "isUnique": true}, "idx_user_email_verification_codes_verification_code": {"name": "idx_user_email_verification_codes_verification_code", "columns": ["verification_code"], "isUnique": true}}, "foreignKeys": {"user_email_verification_codes_user_id_users_id_fk": {"name": "user_email_verification_codes_user_id_users_id_fk", "tableFrom": "user_email_verification_codes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_reset_password_codes": {"name": "user_reset_password_codes", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "used_at": {"name": "used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"user_reset_password_codes_user_id_users_id_fk": {"name": "user_reset_password_codes_user_id_users_id_fk", "tableFrom": "user_reset_password_codes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "account_created_at": {"name": "account_created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "user_type": {"name": "user_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'regular'"}}, "indexes": {"idx_users_email": {"name": "idx_users_email", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}