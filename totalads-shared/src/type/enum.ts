export enum TaskQueueServiceInitializationMode {
	QUEUE = "queue",
	WORKE<PERSON> = "worker",
}

export enum TaskQueueServiceType {
	QUEUE,
	WORKER,
	BOTH,
}

export enum InternalDiscordNotificationTypes {
	GENERAL = "general",
	FAILURE = "failure",
}

export enum NotificationType {
	EMAIL = "email",
	INTERNAL_DISCORD = "internal_discord",
}

export enum AllowedMappedToColumns {
	NAME = "name",
	PHONE_NUMBER = "phone_number",
	EMAIL = "email",
	LINKEDIN_USER_PROFILE = "linkedin_user_profile",
	LINKEDIN_COMPANY_PROFILE = "linkedin_company_profile",
	COMPANY_WEBSITE_URL = "company_website_url",
}

export enum BatchRunMethod {
	IMMEDIATE = "immediate",
	MANUAL = "manual",
	SCHEDULE = "schedule",
}

export enum UserSentiment {
	SKEPTICISM = 1,
	FRUSTRATION = 2,
	ANGER = 3,
	NEUTRALITY = 4,
	CURIOSITY = 5,
	EXCITEMENT = 6,
	DISAPPOINTMENT = 7,
	FULFILLMENT = 8,
}

export enum UserTypes {
	REGULAR = "regular",
	ADMIN = "admin",
}
