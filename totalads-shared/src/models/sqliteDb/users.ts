import {
	integer,
	text,
	sqliteTable,
	uniqueIndex,
} from "drizzle-orm/sqlite-core";

import { UserTypes } from "../../type/enum.ts";

export const users = sqliteTable(
	"users",
	{
		id: integer("id").primaryKey({ autoIncrement: true }),
		name: text("name"),
		email: text("email").notNull(),
		accountCreatedAt: integer("account_created_at", { mode: "timestamp" })
			.$defaultFn(() => new Date())
			.notNull(),
		password: text("password").notNull(),
		emailVerified: integer("email_verified", { mode: "boolean" }).notNull().default(false),
		userType: text("user_type").notNull().default(UserTypes.REGULAR),
	},
	(users) => ({
		emailIdx: uniqueIndex("idx_users_email").on(users.email),
	}),
);
