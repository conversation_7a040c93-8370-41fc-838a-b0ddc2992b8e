import { integer, text, sqliteTable, uniqueIndex } from "drizzle-orm/sqlite-core";
import { users } from "./users";

export const userEmailVerificationCodes = sqliteTable(
	"user_email_verification_codes",
	{
		userId: integer("user_id")
			.notNull()
			.references(() => users.id),
		verificationCode: text("verification_code").notNull(),
		createdAt: integer("created_at", { mode: "timestamp" })
			.$defaultFn(() => new Date())
			.notNull(),
		expiresAt: integer("expires_at", { mode: "timestamp" })
			.$defaultFn(() => new Date())
			.notNull(),
	},
	(userEmailVerificationCodes) => ({
		userIdIdx: uniqueIndex("idx_user_email_verification_codes_user_id").on(
			userEmailVerificationCodes.userId,
		),
		verificationCodeIdx: uniqueIndex(
			"idx_user_email_verification_codes_verification_code",
		).on(userEmailVerificationCodes.verificationCode),
	}),
);
