import { integer, text, sqliteTable, uniqueIndex } from "drizzle-orm/sqlite-core";
import { users } from "./users";

export const apiTokens = sqliteTable(
	"api_tokens",
	{
		id: integer("id").primaryKey({ autoIncrement: true }),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id),
		token: text("token").notNull(),
		name: text("name").notNull(),
		active: integer("active", { mode: "boolean" }).default(true).notNull(),
		createdAt: integer("created_at", { mode: "timestamp" })
			.$defaultFn(() => new Date())
			.notNull(),
		lastUsedAt: integer("last_used_at", { mode: "timestamp" }),
		revokedAt: integer("revoked_at", { mode: "timestamp" }),
	},
	(apiTokens) => ({
		tokenIdx: uniqueIndex("idx_api_tokens_token").on(apiTokens.token),
		userIdIdx: uniqueIndex("idx_api_tokens_user_id").on(apiTokens.userId),
	}),
);
