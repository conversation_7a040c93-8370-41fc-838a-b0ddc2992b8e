import { integer, text, sqliteTable } from "drizzle-orm/sqlite-core";
import { users } from "./users";

/**
 * Schema for the userResetPasswordCodes table, which stores password reset codes and their expiration
 */
export const userResetPasswordCodes = sqliteTable("user_reset_password_codes", {
	id: text("id").primaryKey(),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	code: text("code").notNull(),
	expiresAt: integer("expires_at", { mode: "timestamp" }).notNull(),
	createdAt: integer("created_at", { mode: "timestamp" })
		.$defaultFn(() => new Date())
		.notNull(),
	updatedAt: integer("updated_at", { mode: "timestamp" })
		.$defaultFn(() => new Date())
		.notNull(),
	usedAt: integer("used_at", { mode: "timestamp" }),
	attempts: integer("attempts").default(0),
});
