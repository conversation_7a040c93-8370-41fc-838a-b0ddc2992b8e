import { integer, text, sqliteTable } from "drizzle-orm/sqlite-core";
import { users } from "./users";
import { apiTokens } from "./apiTokens";

export const apiUsage = sqliteTable("api_usage", {
	id: integer("id").primaryKey({ autoIncrement: true }),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id),
	tokenId: integer("token_id").references(() => apiTokens.id),
	endpoint: text("endpoint").notNull(),
	requestId: text("request_id").notNull(),
	timestamp: integer("timestamp", { mode: "timestamp" })
		.$defaultFn(() => new Date())
		.notNull(),
	responseStatus: integer("response_status"),
	charged: integer("charged", { mode: "boolean" }).default(false).notNull(),
	billable: integer("billable", { mode: "boolean" }).default(true).notNull(),
	month: text("month").notNull(),
});
