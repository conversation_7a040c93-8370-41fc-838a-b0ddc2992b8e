import { bigint, integer, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

import { users } from './users';

/**
 * Schema for the userResetPasswordCodes table, which stores password reset codes and their expiration
 */
export const userResetPasswordCodes = pgTable("user_reset_password_codes", {
	id: text("id").primaryKey(),
	userId: bigint("user_id", { mode: "bigint" })
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	code: text("code").notNull(),
	expiresAt: timestamp("expires_at", { precision: 3 }).notNull(),
	createdAt: timestamp("created_at", { precision: 3 }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { precision: 3 }).defaultNow().notNull(),
	usedAt: timestamp("used_at", { precision: 3 }),
	attempts: integer("attempts").default(0),
});
