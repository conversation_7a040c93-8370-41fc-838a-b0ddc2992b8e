import {
	bigint,
	boolean,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

import { users } from "./users";

export const apiTokens = pgTable(
	"api_tokens",
	{
		id: bigint("id", { mode: "bigint" }).primaryKey(),
		userId: bigint("user_id", { mode: "bigint" }).notNull().references(() => users.id),
		token: text("token").notNull(),
		name: text("name").notNull(),
		active: boolean("active").notNull().default(true),
		createdAt: timestamp("created_at", { mode: "date" }).defaultNow().notNull(),
		lastUsedAt: timestamp("last_used_at", { mode: "date" }),
		revokedAt: timestamp("revoked_at", { mode: "date" }),
	},
	(apiTokens) => ({
		tokenIdx: uniqueIndex("idx_api_tokens_token").on(apiTokens.token),
		userIdIdx: uniqueIndex("idx_api_tokens_user_id").on(apiTokens.userId),
	})
);

export const apiTokensRelations = relations(apiTokens, ({ one }) => ({
	user: one(users, {
		fields: [apiTokens.userId],
		references: [users.id],
	}),
}));
