import {
	bigserial,
	boolean,
	pgEnum,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
} from "drizzle-orm/pg-core";

import { UserTypes } from "../../type/enum.ts";

// Create enum for user types
export const userTypesEnum = pgEnum('user_types', ['regular', 'admin']);

export const users = pgTable(
	"users",
	{
		id: bigserial("id", {
			mode: "bigint",
		}).primaryKey(),
		name: text("name"),

		email: text("email").notNull(),
		accountCreatedAt: timestamp("account_created_at", {
			mode: "date",
		})
			.defaultNow()
			.notNull(),
		password: text("password").notNull(),
		emailVerified: boolean("email_verified").notNull().default(false),
		userType: userTypesEnum("user_type").notNull().default(UserTypes.REGULAR),
	},
	(users) => ({
		emailIdx: uniqueIndex("idx_users_email").on(users.email),
	}),
);
