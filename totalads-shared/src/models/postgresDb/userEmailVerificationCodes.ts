import {
	bigint,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
} from "drizzle-orm/pg-core";

import { users } from "./users";

export const userEmailVerificationCodes = pgTable(
	"user_email_verification_codes",
	{
		userId: bigint("user_id", { mode: "bigint" })
			.notNull()
			.references(() => users.id),
		verificationCode: text("verification_code").notNull(),
		createdAt: timestamp("created_at", { mode: "date" })
			.defaultNow()
			.notNull(),
		expiresAt: timestamp("expires_at", { mode: "date" })
			.defaultNow()
			.notNull(),
	},
	(userEmailVerificationCodes) => ({
		userIdIdx: uniqueIndex("idx_user_email_verification_codes_user_id").on(
			userEmailVerificationCodes.userId
		),
		verificationCodeIdx: uniqueIndex(
			"idx_user_email_verification_codes_verification_code"
		).on(userEmailVerificationCodes.verificationCode),
	})
);
