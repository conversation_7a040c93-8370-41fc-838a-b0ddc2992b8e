import {
	bigint,
	boolean,
	integer,
	pgTable,
	timestamp,
	text,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

import { apiTokens } from "./apiTokens";
import { users } from "./users";

export const apiUsage = pgTable(
	"api_usage",
	{
		id: bigint("id", { mode: "bigint" }).primary<PERSON>ey(),
		userId: bigint("user_id", { mode: "bigint" }).notNull().references(() => users.id),
		tokenId: bigint("token_id", { mode: "bigint" }).references(() => apiTokens.id),
		endpoint: text("endpoint").notNull(),
		requestId: text("request_id").notNull(),
		timestamp: timestamp("timestamp", { mode: "date" }).defaultNow().notNull(),
		responseStatus: integer("response_status"),
		charged: boolean("charged").notNull().default(false),
		billable: boolean("billable").notNull().default(true),
		month: text("month").notNull(), // Format: YYYY-MM for easier querying
	}
);

export const apiUsageRelations = relations(apiUsage, ({ one }) => ({
	user: one(users, {
		fields: [apiUsage.userId],
		references: [users.id],
	}),
	token: one(apiTokens, {
		fields: [apiUsage.tokenId],
		references: [apiTokens.id],
	}),
}));
