import { apiTokens as pgApiTokens } from './postgresDb/apiTokens';
import { apiUsage as pgApiUsage } from './postgresDb/apiUsage';
import {
    userEmailVerificationCodes as pgUserEmailVerificationCodes
} from './postgresDb/userEmailVerificationCodes';
import {
    userResetPasswordCodes as pgUserResetPasswordCodes
} from './postgresDb/userResetPasswordCodes';
import { users as pgUsers } from './postgresDb/users';
import { apiTokens as sqliteApiTokens } from './sqliteDb/apiTokens';
import { apiUsage as sqliteApiUsage } from './sqliteDb/apiUsage';
import {
    userEmailVerificationCodes as sqliteUserEmailVerificationCodes
} from './sqliteDb/userEmailVerificationCodes';
import {
    userResetPasswordCodes as sqliteUserResetPasswordCodes
} from './sqliteDb/userResetPasswordCodes';
// Import all schemas and re-export the appropriate ones based on database type
import { users as sqliteUsers } from './sqliteDb/users';

// Determine which schema to use based on database type
const isUsingSQLite = process.env.DB_URL?.startsWith("file:");

// Export the appropriate schema
export const users = isUsingSQLite ? sqliteUsers : pgUsers;
export const apiTokens = isUsingSQLite ? sqliteApiTokens : pgApiTokens;
export const apiUsage = isUsingSQLite ? sqliteApiUsage : pgApiUsage;
export const userEmailVerificationCodes = isUsingSQLite
	? sqliteUserEmailVerificationCodes
	: pgUserEmailVerificationCodes;
export const userResetPasswordCodes = isUsingSQLite
	? sqliteUserResetPasswordCodes
	: pgUserResetPasswordCodes;
