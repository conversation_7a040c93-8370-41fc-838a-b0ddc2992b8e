import { eq } from 'drizzle-orm';
import { NextFunction, Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';

import { HTTPError } from '../../config/error.ts';
import postgresDb from '../../config/postgresDb.ts';
import { users } from '../../models/postgresDb/users.ts';
import { UserTypes } from '../../type/enum.ts';
import { verifyJWT } from '../../utils/jwt.ts';
const authMiddlewareCreator =
	(userTypesAllowed: UserTypes[] = []) =>
	async (req: Request, res: Response, next: NextFunction) => {
		const UnauthorizedError = new HTTPError({
			httpStatus: StatusCodes.UNAUTHORIZED,
		});
		const ForbiddenError = new HTTPError({
			httpStatus: StatusCodes.FORBIDDEN,
		});
		const authToken = req.cookies.auth;
		if (!authToken) return next(UnauthorizedError);
		try {
			const payload = await verifyJWT(authToken);
			const userId = BigInt(payload.id);
			const result = await postgresDb
				.select({
					userType: users.userType,
				})
				.from(users)
				.where(eq(users.id, userId))
				.limit(1);
			if (!result.length) {
				console.log("🔴 Someone is trying to hack us!!!");
				throw UnauthorizedError;
			}
			// Convert the string userType from database to enum value
			const userType = result[0].userType as UserTypes;
			if (userTypesAllowed.length && !userTypesAllowed.includes(userType))
				return next(ForbiddenError);
			req.userId = userId;
			return next();
		} catch {
			return next(UnauthorizedError);
		}
	};

export default authMiddlewareCreator;
