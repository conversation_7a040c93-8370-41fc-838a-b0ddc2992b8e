import '../config/postgresDb.ts';

import cookieParser from 'cookie-parser';
import cors from 'cors';
import express, { Express, Request, RequestHandler, Response, response } from 'express';
import { getReasonPhrase, ReasonPhrases, StatusCodes } from 'http-status-codes';
import morgan from 'morgan';
import swaggerUi, { JsonObject } from 'swagger-ui-express';

import { HTTPError } from '../config/error.ts';

const CORS_ALLOWED_DOMAINS = ["http://localhost:3000"];

interface Route {
	path: string;
	handlers: RequestHandler[];
}

interface ServerInitData {
	name: string;
	port: number;
	routes: Route[];
	swaggerJSON?: JsonObject | null;
}

export default class Server {
	app: Express;
	name: string;
	port: number;
	routes: Route[];
	swaggerJSON?: JsonObject | null;

	constructor({ name, port, routes, swaggerJSON = null }: ServerInitData) {
		const app = express();
		this.app = app;
		this.name = name;
		this.port = port;
		this.routes = routes;
		this.swaggerJSON = swaggerJSON;
		this.setupServer();
	}

	async setupServer() {
		this.setupSwagger();
		this.setupPreRoutesSetupMiddlewares();
		this.setupRoutes();
		this.setupPostRoutesSetupMiddlewares();
		this.startListening();
	}

	setupSwagger() {
		if (this.swaggerJSON) {
			console.log("Setting up swagger UI URL");
			this.app.use(
				"/api-docs",
				swaggerUi.serve,
				swaggerUi.setup(this.swaggerJSON)
			);
		}
	}

	setupPreRoutesSetupMiddlewares() {
		this.app.use(
			cors({
				origin(requestOrigin, callback) {
					callback(
						null,
						process.env.ENV === "prod"
							? CORS_ALLOWED_DOMAINS.includes(requestOrigin!)
							: true
					);
				},
				credentials: true,
			})
		);
		this.app.use(
			express.json({
				verify: (req, res, buf) => {
					(req as any).rawBody = buf;
				},
			})
		);
		this.app.use(cookieParser());
		this.app.use(
			morgan(
				":remote-addr :remote-user :method :url HTTP/:http-version :status - :response-time ms"
			)
		);

		this.app.use((req, res, next) => {
			const actualSendMethod = res.send;
			res.send = (data) => {
				let payload;
				try {
					payload = JSON.parse(data);
				} catch {
					payload = data;
				}
				const statusCode = res.statusCode;
				res.send = actualSendMethod;
				return res.status(statusCode).json({
					status: statusCode,
					message: getReasonPhrase(statusCode),
					payload,
				});
			};
			next();
		});
	}

	setupRoutes() {
		this.setupHealthRoute();
		for (const route of this.routes)
			this.app.use(route.path, ...route.handlers);
	}

	setupHealthRoute() {
		this.app.get("/health", (_req, res) => {
			res.status(200).send("All good!");
		});
	}

	setupPostRoutesSetupMiddlewares() {
		this.app.use((req, res, next) => {
			const notFoundError = new HTTPError({
				httpStatus: StatusCodes.NOT_FOUND,
			});
			next(notFoundError);
		});

		this.app.use((err: Error, req: Request, res: Response, next: Function) => {
			let status, data;
			res.send = response.send;
			if (err instanceof HTTPError) {
				status = err.httpStatus;
				data = {
					status: err.httpStatus,
					error: err.message
						? err.message
						: getReasonPhrase(err.httpStatus),
					reason: err.reason || null,
				};
			} else {
				status = StatusCodes.INTERNAL_SERVER_ERROR;
				data = {
					status: StatusCodes.INTERNAL_SERVER_ERROR,
					error: ReasonPhrases.INTERNAL_SERVER_ERROR,
				};
			}
			res.status(status).json(data);
		});
	}

	startListening() {
		const port = this.port;
		try {
			const server = this.app.listen(port, '0.0.0.0', () => {
				console.log(`Server ${this.name} is 📡 on port ${port} 🟢`);
				console.log(`Server address: ${JSON.stringify(server.address())}`);
			});
			
			// Add error handler for the server
			server.on('error', (err: any) => {
				if (err.code === 'EADDRINUSE') {
					console.error(`❌ Port ${port} is already in use. Cannot start server.`);
				} else {
					console.error(`❌ Server error: ${err.message}`, err);
				}
				process.exit(1);
			});
			
			// Handle process termination gracefully
			process.on('SIGTERM', () => {
				console.log('SIGTERM received. Shutting down gracefully...');
				server.close(() => {
					console.log('Server closed.');
				});
			});
			
			return server;
		} catch (error: any) {
			console.error(`❌ Failed to start server on port ${port}: ${error.message}`);
			process.exit(1);
		}
	}
}
