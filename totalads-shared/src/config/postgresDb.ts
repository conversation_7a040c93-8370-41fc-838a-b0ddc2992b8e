import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { drizzle as drizzlePostgres, PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Check if we're using SQLite (for local development) or PostgreSQL (for production)
const isUsingSQLite = process.env.DB_URL?.startsWith("file:");

let db: any;

if (isUsingSQLite) {
	// SQLite for local development
	const sqlite = new Database(process.env.DB_URL!.replace("file:", ""));
	db = drizzle(sqlite);
	console.log("Connected to SQLite database 📁");
} else {
	// PostgreSQL for production
	const isLocal =
		process.env.DB_URL?.includes("localhost") ||
		process.env.DB_URL?.includes("127.0.0.1");
	const queryClient = postgres(process.env.DB_URL as string, {
		ssl: isLocal ? false : "require",
	});
	db = drizzlePostgres(queryClient);
	console.log("Connected to PostgreSQL database 🐘");
}

export default db;
