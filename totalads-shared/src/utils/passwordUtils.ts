import { hash, verify } from '@node-rs/bcrypt';

/**
 * Hash a password using bcrypt with a salt of 10 rounds
 * @param password Plain text password to hash
 * @returns Hashed password
 */
export const hashPassword = async (password: string): Promise<string> => {
  return hash(password, 10);
};

/**
 * Verify if a plain text password matches a hashed password
 * @param plainTextPassword Plain text password to verify
 * @param hashedPassword Hashed password to compare against
 * @returns <PERSON>olean indicating if the password matches
 */
export const verifyPassword = async (
  plainTextPassword: string,
  hashedPassword: string
): Promise<boolean> => {
  return verify(plainTextPassword, hashedPassword);
};
