import { Resend } from "resend";

// Create a mock Resend client for development when no API key is provided
class MockResendClient {
  async send(data: any) {
    console.log('📧 MOCK EMAIL SENT:', data);
    return { id: 'mock-email-id', data };
  }
}

// Initialize Resend with API key or use mock implementation
let emailClient: any;

try {
  const apiKey = process.env.RESEND_API_KEY || '';
  
  if (!apiKey || apiKey === 're_fake_development_key_for_mock_resend_client') {
    console.log('⚠️ Using mock email client - no valid Resend API key provided');
    emailClient = { emails: new MockResendClient() };
  } else {
    emailClient = new Resend(apiKey);
  }
} catch (error) {
  console.error('Failed to initialize Resend client:', error);
  console.log('⚠️ Falling back to mock email client');
  emailClient = { emails: new MockResendClient() };
}

interface SendEmailData {
  email: string;
  subject: string;
  html: string;
}

const sendEmail = async ({ email, subject, html }: SendEmailData) => {
  try {
    await emailClient.emails.send({
      from: "<EMAIL>",
      to: email,
      subject,
      html,
    });
    console.log(`Email sent to ${email} with subject: ${subject}`);
  } catch (error) {
    console.error('Failed to send email:', error);
  }
};

export default sendEmail;
