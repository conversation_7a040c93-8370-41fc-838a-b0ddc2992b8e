"use client";
import { ThemeProvider } from "next-themes";
import React from "react";
import { ScraperProvider } from "@/context/ScraperContext";
import { AuthProvider } from "@/context/AuthContext";
import { TokenProvider } from "@/context/TokenContext";

const Provider = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <AuthProvider>
        <TokenProvider>
          <ScraperProvider>
            {children}
          </ScraperProvider>
        </TokenProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default Provider;
