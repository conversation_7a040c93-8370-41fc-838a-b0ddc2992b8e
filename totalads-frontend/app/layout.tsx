/* eslint-disable @next/next/no-sync-scripts */
import type { Metadata } from "next";
import "./globals.css";

import { Nunito_Sans } from "next/font/google";

import { cn } from "@/utils/cn";

import Provider from "./provider";
import TopNav from "@/components/navigation/TopNav";

const inter = Nunito_Sans({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TotalAds - Website Scraper & Data Extraction",
  description: "Extract valuable information from any website with our powerful scraper",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, ["bg-bg"])}>
        <Provider>
          <div className="flex flex-col min-h-screen">
            <TopNav />
            <main className="flex-grow">{children}</main>
            <footer className="bg-bg-900 text-white py-4 text-center text-sm">
              <div className="container mx-auto">
                <p>© {new Date().getFullYear()} TotalAds. All rights reserved.</p>
              </div>
            </footer>
          </div>
        </Provider>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.js"></script>
      </body>
    </html>
  );
}
