"use client";

import React, { useEffect, useState } from 'react';
import { useAuthContext } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { IconApi, IconChartBar, IconClock, IconCreditCard, IconDatabase, IconKey, IconPlus, IconUser } from '@tabler/icons-react';

interface DashboardStats {
  totalApiCalls: number;
  remainingCalls: number;
  totalTokens: number;
  lastActivity: string;
}

export default function Dashboard() {
  const { state } = useAuthContext();
  const { isAuthenticated, isLoading, user } = state;
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalApiCalls: 0,
    remainingCalls: 10,
    totalTokens: 0,
    lastActivity: 'Never'
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-bg-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-2">
            Welcome back, {user?.name || 'User'}!
          </h1>
          <p className="text-text-200">
            Manage your scraping operations and API usage from your dashboard.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-200 text-sm font-medium">API Calls Used</p>
                <p className="text-2xl font-bold text-text">{stats.totalApiCalls}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconApi className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-200 text-sm font-medium">Remaining Calls</p>
                <p className="text-2xl font-bold text-text">{stats.remainingCalls}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <IconChartBar className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-200 text-sm font-medium">API Tokens</p>
                <p className="text-2xl font-bold text-text">{stats.totalTokens}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <IconKey className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-200 text-sm font-medium">Last Activity</p>
                <p className="text-sm font-medium text-text">{stats.lastActivity}</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <IconClock className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <h2 className="text-xl font-semibold text-text mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <Link
                href="/scraper"
                className="flex items-center p-3 rounded-lg border border-bg-200 hover:bg-bg-50 transition-colors"
              >
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                  <IconDatabase className="w-5 h-5 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-medium text-text">Start Scraping</h3>
                  <p className="text-sm text-text-200">Extract data from websites</p>
                </div>
              </Link>

              <Link
                href="/api-tokens"
                className="flex items-center p-3 rounded-lg border border-bg-200 hover:bg-bg-50 transition-colors"
              >
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <IconPlus className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-text">Create API Token</h3>
                  <p className="text-sm text-text-200">Generate new API access token</p>
                </div>
              </Link>

              <Link
                href="/scraper/history"
                className="flex items-center p-3 rounded-lg border border-bg-200 hover:bg-bg-50 transition-colors"
              >
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <IconClock className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-text">View History</h3>
                  <p className="text-sm text-text-200">Check your scraping history</p>
                </div>
              </Link>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <h2 className="text-xl font-semibold text-text mb-4">Account Overview</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-text-200">Plan</span>
                <span className="font-medium text-text">Free Tier</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-text-200">Monthly Limit</span>
                <span className="font-medium text-text">10 calls</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-text-200">Used This Month</span>
                <span className="font-medium text-text">{stats.totalApiCalls} calls</span>
              </div>
              <div className="w-full bg-bg-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full" 
                  style={{ width: `${(stats.totalApiCalls / 10) * 100}%` }}
                ></div>
              </div>
              <div className="pt-2">
                <Link
                  href="/settings/billing"
                  className="flex items-center text-primary-600 hover:text-primary-700 font-medium"
                >
                  <IconCreditCard className="w-4 h-4 mr-1" />
                  Upgrade to Pro
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
          <h2 className="text-xl font-semibold text-text mb-4">Recent Activity</h2>
          <div className="text-center py-8">
            <IconDatabase className="w-12 h-12 text-text-200 mx-auto mb-4" />
            <p className="text-text-200">No recent activity</p>
            <p className="text-sm text-text-200 mt-1">
              Start scraping to see your activity here
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
