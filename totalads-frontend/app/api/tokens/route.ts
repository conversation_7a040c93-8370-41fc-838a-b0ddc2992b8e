import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * GET /api/tokens
 * Get all API tokens for the current user
 */
export async function GET(req: NextRequest) {
  try {
    // Get the auth token from cookies or auth header
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Forward the request to the backend API
    const response = await fetch(`${apiUrl}/api/tokens`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error fetching API tokens:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch API tokens' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tokens
 * Create a new API token
 */
export async function POST(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();
    
    // Validate request
    if (!body.name) {
      return NextResponse.json(
        { success: false, message: 'Token name is required' },
        { status: 400 }
      );
    }

    // Get the auth token
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Forward the request to create a token
    const response = await fetch(`${apiUrl}/api/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error creating API token:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create API token' },
      { status: 500 }
    );
  }
}
