import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * GET /api/tokens/[id]/usage
 * Get usage statistics for a specific API token
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Get the auth token
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters (start and end dates)
    const searchParams = req.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    
    // Build the URL with query parameters if provided
    let url = `${apiUrl}/api/tokens/${id}/usage`;
    const queryParams = [];
    
    if (startDate) {
      queryParams.push(`startDate=${encodeURIComponent(startDate)}`);
    }
    
    if (endDate) {
      queryParams.push(`endDate=${encodeURIComponent(endDate)}`);
    }
    
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }

    // Forward the request to get token usage
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error(`Error fetching usage for token with id ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch token usage statistics' },
      { status: 500 }
    );
  }
}
