import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * GET /api/tokens/[id]
 * Get details of a specific API token
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Get the auth token
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Forward the request to get token details
    const response = await fetch(`${apiUrl}/api/tokens/${id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error(`Error fetching token with id ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch API token' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tokens/[id]
 * Delete a specific API token
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Get the auth token
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Forward the request to delete the token
    const response = await fetch(`${apiUrl}/api/tokens/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error(`Error deleting token with id ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete API token' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/tokens/[id]
 * Update a specific API token (e.g., rename)
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await req.json();
    
    // Get the auth token
    const authToken = cookies().get('authToken')?.value || 
      req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Forward the request to update the token
    const response = await fetch(`${apiUrl}/api/tokens/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    // Forward the response with the same status
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error(`Error updating token with id ${params.id}:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update API token' },
      { status: 500 }
    );
  }
}
