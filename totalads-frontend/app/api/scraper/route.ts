import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/scraper
 * Forward scrape requests to the backend API
 */
export async function POST(req: NextRequest) {
  try {
    const { url, enableAI } = await req.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Get authentication token from cookies or headers
    // This is a placeholder for now - we'll implement proper auth later
    const authToken = req.cookies.get('authToken')?.value || req.headers.get('Authorization')?.replace('Bearer ', '');

    if (!authToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Forward the request to the actual API
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${apiUrl}/api/scraper`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ url, enableAI: enableAI || false }),
    });

    const data = await response.json();

    // Forward the status code from the API
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error in scraper API route:', error);
    return NextResponse.json(
      { error: 'Failed to process scraper request' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/scraper/health
 * Check health of the scraper service
 */
export async function GET() {
  try {
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    
    // Forward the request to the health check endpoint
    const response = await fetch(`${apiUrl}/api/scraper/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 60 } // Cache for 60 seconds
    });

    const data = await response.json();

    // Forward the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error checking scraper health:', error);
    return NextResponse.json(
      { 
        success: false,
        status: 'down',
        error: 'Failed to check scraper health'
      },
      { status: 500 }
    );
  }
}
