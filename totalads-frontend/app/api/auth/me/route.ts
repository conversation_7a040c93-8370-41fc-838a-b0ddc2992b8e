import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/auth/me
 * Get the currently authenticated user's information
 */
export async function GET(req: NextRequest) {
  try {
    // Get token from cookie or auth header
    const authToken =
      cookies().get("auth")?.value ||
      req.headers.get("Authorization")?.replace("Bearer ", "");

    if (!authToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized: No auth token provided" },
        { status: 401 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";

    // Forward the request to get the user data
    const response = await fetch(`${apiUrl}/users/me`, {
      method: "GET",
      headers: {
        Cookie: `auth=${authToken}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      // If token is invalid or expired, clear the cookie
      if (response.status === 401) {
        cookies().delete("auth");
      }

      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to get user information",
        },
        { status: response.status }
      );
    }

    // Return user data
    return NextResponse.json({
      success: true,
      user: data.user,
    });
  } catch (error) {
    console.error("Get user error:", error);
    return NextResponse.json(
      { success: false, message: "An error occurred while fetching user data" },
      { status: 500 }
    );
  }
}
