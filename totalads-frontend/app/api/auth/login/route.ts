import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/auth/login
 * Handle user login authentication
 */
export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();

    // Basic validation
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: "Email and password are required" },
        { status: 400 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";

    // Forward the request to the actual auth API endpoint
    const response = await fetch(`${apiUrl}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { success: false, message: errorText || "Authentication failed" },
        { status: response.status }
      );
    }

    // Backend returns success message, we need to get user data separately
    const successMessage = await response.text();

    // Get cookies from the response to forward them
    const setCookieHeader = response.headers.get("set-cookie");

    if (setCookieHeader) {
      // Forward the auth cookie from backend
      const authCookie = setCookieHeader.split(";")[0]; // Get the main cookie part
      const cookieValue = authCookie.split("=")[1]; // Extract the value

      // Set the cookie in our response
      cookies().set({
        name: "auth",
        value: cookieValue,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: 60 * 60 * 24 * 30, // 30 days to match backend
      });
    }

    // Get user data using the forwarded cookie
    const userResponse = await fetch(`${apiUrl}/users/me`, {
      method: "GET",
      headers: {
        Cookie: setCookieHeader || "",
      },
    });

    let user = null;
    if (userResponse.ok) {
      user = await userResponse.json();
    }

    // Return success response with user data
    return NextResponse.json({
      success: true,
      user,
      message: "Login successful",
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { success: false, message: "An error occurred during login" },
      { status: 500 }
    );
  }
}
