import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/auth/register
 * Handle new user registration
 */
export async function POST(req: NextRequest) {
  try {
    const { name, email, password } = await req.json();

    // Basic validation
    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, message: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";

    // Forward the request to the actual auth API endpoint
    const response = await fetch(`${apiUrl}/auth/signup`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ name, email, password }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { success: false, message: errorText || "Registration failed" },
        { status: response.status }
      );
    }

    // Backend returns success message, we need to get user data separately
    const successMessage = await response.text();

    // Get cookies from the response to forward them
    const setCookieHeader = response.headers.get("set-cookie");

    if (setCookieHeader) {
      // Forward the auth cookie from backend
      const authCookie = setCookieHeader.split(";")[0]; // Get the main cookie part
      const cookieValue = authCookie.split("=")[1]; // Extract the value

      // Set the cookie in our response
      cookies().set({
        name: "auth",
        value: cookieValue,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: 60 * 60 * 24 * 30, // 30 days to match backend
      });
    }

    // Get user data using the forwarded cookie
    const userResponse = await fetch(`${apiUrl}/users/me`, {
      method: "GET",
      headers: {
        Cookie: setCookieHeader || "",
      },
    });

    let user = null;
    if (userResponse.ok) {
      user = await userResponse.json();
    }

    // Return success response with user data
    return NextResponse.json({
      success: true,
      user,
      message: "Registration successful",
    });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { success: false, message: "An error occurred during registration" },
      { status: 500 }
    );
  }
}
