"use client";

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useAuthContext } from '@/context/AuthContext';
import { ApiToken, createToken, deleteToken, listTokens } from '@/utils/api/tokenClient';
import { IconCopy, IconEye, IconEyeOff, IconKey, IconPlus, IconTrash } from '@tabler/icons-react';

export default function ApiTokensPage() {
  const { state } = useAuthContext();
  const { isAuthenticated, isLoading } = state;
  const router = useRouter();

  const [tokens, setTokens] = useState<ApiToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTokenName, setNewTokenName] = useState("");
  const [creating, setCreating] = useState(false);
  const [newToken, setNewToken] = useState<string | null>(null);
  const [visibleTokens, setVisibleTokens] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  useEffect(() => {
    if (isAuthenticated) {
      loadTokens();
    }
  }, [isAuthenticated]);

  const loadTokens = async () => {
    try {
      setLoading(true);
      const tokenList = await listTokens();
      setTokens(tokenList);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load tokens");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateToken = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTokenName.trim()) return;

    try {
      setCreating(true);
      const token = await createToken({ name: newTokenName.trim() });
      setNewToken(token.token || "");
      setTokens([token, ...tokens]);
      setNewTokenName("");
      setShowCreateForm(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create token");
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteToken = async (id: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this token? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteToken(id);
      setTokens(tokens.filter((token) => token.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete token");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const toggleTokenVisibility = (tokenId: string) => {
    const newVisible = new Set(visibleTokens);
    if (newVisible.has(tokenId)) {
      newVisible.delete(tokenId);
    } else {
      newVisible.add(tokenId);
    }
    setVisibleTokens(newVisible);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-bg-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-text mb-2">API Tokens</h1>
            <p className="text-text-200">
              Manage your API tokens to access the TotalAds scraper API.
            </p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <IconPlus className="w-4 h-4 mr-2" />
            Create Token
          </button>
        </div>

        {error && (
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"
            role="alert"
          >
            <div className="flex">
              <p>{error}</p>
              <button onClick={() => setError("")} className="ml-auto">
                ×
              </button>
            </div>
          </div>
        )}

        {newToken && (
          <div
            className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6"
            role="alert"
          >
            <div>
              <p className="font-bold">Token Created Successfully!</p>
              <p className="text-sm mt-1">
                Copy this token now - you won't be able to see it again:
              </p>
              <div className="flex items-center mt-2 p-2 bg-white rounded border">
                <code className="flex-1 text-sm font-mono">{newToken}</code>
                <button
                  onClick={() => copyToClipboard(newToken)}
                  className="ml-2 p-1 hover:bg-gray-100 rounded"
                  title="Copy to clipboard"
                >
                  <IconCopy className="w-4 h-4" />
                </button>
              </div>
              <button
                onClick={() => setNewToken(null)}
                className="mt-2 text-sm underline"
              >
                I've copied the token
              </button>
            </div>
          </div>
        )}

        {showCreateForm && (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200 mb-6">
            <h2 className="text-xl font-semibold text-text mb-4">
              Create New Token
            </h2>
            <form onSubmit={handleCreateToken}>
              <div className="mb-4">
                <label
                  htmlFor="tokenName"
                  className="block text-sm font-medium text-text mb-2"
                >
                  Token Name
                </label>
                <input
                  type="text"
                  id="tokenName"
                  value={newTokenName}
                  onChange={(e) => setNewTokenName(e.target.value)}
                  placeholder="e.g., Production API, Development, Mobile App"
                  className="w-full px-3 py-2 border border-bg-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div className="flex space-x-3">
                <button
                  type="submit"
                  disabled={creating}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 transition-colors"
                >
                  {creating ? "Creating..." : "Create Token"}
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 border border-bg-200 text-text rounded-lg hover:bg-bg-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-sm border border-bg-200">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-text mb-4">
              Your API Tokens
            </h2>

            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="text-text-200 mt-2">Loading tokens...</p>
              </div>
            ) : tokens.length === 0 ? (
              <div className="text-center py-8">
                <IconKey className="w-12 h-12 text-text-200 mx-auto mb-4" />
                <p className="text-text-200">No API tokens found</p>
                <p className="text-sm text-text-200 mt-1">
                  Create your first token to start using the API
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {tokens.map((token) => (
                  <div
                    key={token.id}
                    className="border border-bg-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-text">{token.name}</h3>
                        <div className="flex items-center mt-1 space-x-4 text-sm text-text-200">
                          <span>
                            Created:{" "}
                            {new Date(token.createdAt).toLocaleDateString()}
                          </span>
                          {token.lastUsed && (
                            <span>
                              Last used:{" "}
                              {new Date(token.lastUsed).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                        {token.token && (
                          <div className="flex items-center mt-2">
                            <code className="text-sm font-mono bg-bg-100 px-2 py-1 rounded">
                              {visibleTokens.has(token.id)
                                ? token.token
                                : "••••••••••••••••"}
                            </code>
                            <button
                              onClick={() => toggleTokenVisibility(token.id)}
                              className="ml-2 p-1 hover:bg-bg-100 rounded"
                              title={
                                visibleTokens.has(token.id)
                                  ? "Hide token"
                                  : "Show token"
                              }
                            >
                              {visibleTokens.has(token.id) ? (
                                <IconEyeOff className="w-4 h-4" />
                              ) : (
                                <IconEye className="w-4 h-4" />
                              )}
                            </button>
                            <button
                              onClick={() => copyToClipboard(token.token!)}
                              className="ml-1 p-1 hover:bg-bg-100 rounded"
                              title="Copy to clipboard"
                            >
                              <IconCopy className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => handleDeleteToken(token.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete token"
                      >
                        <IconTrash className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* API Documentation */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-bg-200 p-6">
          <h2 className="text-xl font-semibold text-text mb-4">
            Using Your API Token
          </h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-text mb-2">Authentication</h3>
              <p className="text-text-200 text-sm mb-2">
                Include your API token in the Authorization header:
              </p>
              <code className="block bg-bg-100 p-3 rounded text-sm font-mono">
                Authorization: Bearer YOUR_API_TOKEN
              </code>
            </div>

            <div>
              <h3 className="font-medium text-text mb-2">Example Request</h3>
              <code className="block bg-bg-100 p-3 rounded text-sm font-mono whitespace-pre">
                {`curl -X POST http://localhost:8001/api/scraper \\
  -H "Authorization: Bearer YOUR_API_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "url": "https://example.com",
    "enableAI": false
  }'`}
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
