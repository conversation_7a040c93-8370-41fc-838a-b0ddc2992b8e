"use client";

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import GetLogo from '@/components/common/getLogo';
import { useAuthContext } from '@/context/AuthContext';

export default function Home() {
  const { state } = useAuthContext();
  const { isAuthenticated, isLoading } = state;
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push("/dashboard");
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-bg-50 to-bg-100">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <div className="flex items-center justify-center mb-8">
            <GetLogo className="h-16 w-16 mr-4" />
            <h1 className="text-5xl font-bold text-text">TotalAds</h1>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-text mb-6">
            Powerful Website Scraper & Data Extraction
          </h2>

          <p className="text-xl text-text-200 mb-8 max-w-2xl mx-auto">
            Extract valuable business intelligence from any website with our
            AI-powered scraper. Get comprehensive data including contact
            details, company information, and business insights.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              href="/signup"
              className="px-8 py-3 bg-primary-600 text-white rounded-lg font-semibold hover:bg-primary-700 transition-colors"
            >
              Get Started Free
            </Link>
            <Link
              href="/login"
              className="px-8 py-3 border border-primary-600 text-primary-600 rounded-lg font-semibold hover:bg-primary-50 transition-colors"
            >
              Sign In
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-text mb-2">
              AI-Powered Extraction
            </h3>
            <p className="text-text-200">
              Advanced AI algorithms extract comprehensive business intelligence
              and contact information from any website.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-text mb-2">
              Lightning Fast
            </h3>
            <p className="text-text-200">
              Get results in seconds with our optimized scraping infrastructure
              and intelligent caching.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-bg-200">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-text mb-2">API Access</h3>
            <p className="text-text-200">
              Integrate our scraping capabilities into your applications with
              our robust REST API.
            </p>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-text mb-8">
            Simple, Transparent Pricing
          </h3>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-bg-200">
              <h4 className="text-xl font-semibold text-text mb-2">
                Free Tier
              </h4>
              <div className="text-3xl font-bold text-primary-600 mb-4">$0</div>
              <ul className="text-left space-y-2 text-text-200 mb-6">
                <li>✓ 10 API calls per month</li>
                <li>✓ Basic data extraction</li>
                <li>✓ Email support</li>
                <li>✓ API documentation</li>
              </ul>
              <Link
                href="/signup"
                className="block w-full py-2 px-4 bg-bg-100 text-text rounded-lg font-semibold hover:bg-bg-200 transition-colors"
              >
                Get Started
              </Link>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border-2 border-primary-600 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary-600 text-white px-4 py-1 rounded-full text-sm">
                Popular
              </div>
              <h4 className="text-xl font-semibold text-text mb-2">Pro Tier</h4>
              <div className="text-3xl font-bold text-primary-600 mb-4">
                $0.05<span className="text-lg text-text-200">/call</span>
              </div>
              <ul className="text-left space-y-2 text-text-200 mb-6">
                <li>✓ Unlimited API calls</li>
                <li>✓ AI-enhanced extraction</li>
                <li>✓ Priority support</li>
                <li>✓ Advanced analytics</li>
              </ul>
              <Link
                href="/signup"
                className="block w-full py-2 px-4 bg-primary-600 text-white rounded-lg font-semibold hover:bg-primary-700 transition-colors"
              >
                Start Pro Trial
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
