"use client";

import React, { useState } from 'react';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

interface ScraperFormProps {
  onSubmit: (url: string, enableAI: boolean) => void;
  isLoading: boolean;
  onReset?: () => void;
}

const ScraperForm: React.FC<ScraperFormProps> = ({ onSubmit, isLoading, onReset }) => {
  const [url, setUrl] = useState<string>('');
  const [enableAI, setEnableAI] = useState<boolean>(false);
  const [urlError, setUrlError] = useState<string | null>(null);

  const validateUrl = (value: string): boolean => {
    try {
      new URL(value);
      return true;
    } catch (e) {
      return false;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!url.trim()) {
      setUrlError('URL is required');
      return;
    }

    // Check if URL is valid
    if (!validateUrl(url)) {
      setUrlError('Please enter a valid URL (e.g., https://example.com)');
      return;
    }

    setUrlError(null);
    onSubmit(url, enableAI);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="url" className="text-lg font-medium">
          Website URL
        </Label>
        <Input
          id="url"
          type="text"
          placeholder="https://example.com"
          value={url}
          onChange={(e) => {
            setUrl(e.target.value);
            if (urlError) setUrlError(null);
          }}
          className="w-full"
          disabled={isLoading}
        />
        {urlError && (
          <p className="text-sm text-red-500 mt-1">{urlError}</p>
        )}
        <p className="text-sm text-text-200 mt-1">
          Enter the full URL of the website you want to scrape, including the http:// or https:// prefix.
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="ai-processing"
          checked={enableAI}
          onCheckedChange={(checked) => setEnableAI(checked === true)}
          disabled={isLoading}
        />
        <Label htmlFor="ai-processing" className="text-sm font-medium cursor-pointer">
          Enable AI processing (summarize content and extract additional insights)
        </Label>
      </div>
      
      <div className="flex items-center space-x-4">
        <button
          type="submit"
          disabled={isLoading}
          className="bg-gradient-to-br relative from-primary-700 to-primary-500 hover:from-primary-600 hover:to-primary-400 px-6 py-2 rounded-md text-white font-medium transition-colors duration-200 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            'Scrape Website'
          )}
        </button>
        
        <button
          type="button"
          onClick={() => {
            setUrl('');
            setEnableAI(false);
            setUrlError(null);
            if (onReset) onReset();
          }}
          disabled={isLoading || !url}
          className="text-text-200 hover:text-text transition-colors duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Clear
        </button>
      </div>
      
      <div className="text-xs text-text-200 mt-2 border-t border-bg-300 pt-4">
        <p>
          <strong>Note:</strong> Scraping may use credits from your account. AI processing uses additional credits.
        </p>
      </div>
    </form>
  );
};

export default ScraperForm;
