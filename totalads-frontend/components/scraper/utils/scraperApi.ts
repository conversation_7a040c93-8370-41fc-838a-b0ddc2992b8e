"use client";

import axios from 'axios';
import { ScrapeR<PERSON>ult, ScraperHealth } from './scraperTypes';
import apiClient from '@/utils/api/apiClient';

/**
 * Submit a URL to scrape
 * @param url The URL to scrape
 * @param enableAI Whether to enable AI processing
 * @returns Promise with scrape results
 */
export const scrapeUrl = async (url: string, enableAI: boolean = false): Promise<ScrapeResult> => {
  try {
    const response = await apiClient.post('/api/scraper', { url, enableAI });
    return response.data;
  } catch (error: unknown) {
    console.error('Error scraping URL:', error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data?.error || `Failed to scrape URL: ${error.response.status}`);
    }
    throw error;
  }
};

/**
 * Check the health status of the scraper service
 * @returns Promise with health status
 */
export const checkScraperHealth = async (): Promise<ScraperHealth> => {
  try {
    const response = await apiClient.get('/api/scraper/health');
    return response.data;
  } catch (error: unknown) {
    console.error('Error checking scraper health:', error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data?.error || `Failed to check health: ${error.response.status}`);
    }
    throw error;
  }
};

/**
 * Get user's scraper usage statistics
 * @returns Promise with usage data
 */
export const getScraperUsage = async (): Promise<any> => {
  try {
    const response = await apiClient.get('/api/scraper/usage');
    return response.data;
  } catch (error: unknown) {
    console.error('Error getting scraper usage:', error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data?.error || `Failed to get usage data: ${error.response.status}`);
    }
    throw error;
  }
};
