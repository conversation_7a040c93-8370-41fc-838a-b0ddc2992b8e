"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

import ScraperForm from './ScraperForm';
import ScraperResults from './ScraperResults';
import ScraperHealthIndicator from './ScraperHealthIndicator';
import BillingFeedback from './BillingFeedback';
import { useScraperContext } from '@/context/ScraperContext';
import { useAuthContext } from '@/context/AuthContext';
import { IconHistory } from '@tabler/icons-react';

const ScraperContainer = () => {
  const router = useRouter();
  const { state: authState } = useAuthContext();
  const { state, scrapeWebsite, resetResult } = useScraperContext();
  const { isLoading, error, result } = state;
  const [enableAI, setEnableAI] = useState(false);
  const [showAuthWarning, setShowAuthWarning] = useState(false);
  
  // Check if user is authenticated
  useEffect(() => {
    if (!authState.isAuthenticated && !authState.isLoading) {
      setShowAuthWarning(true);
    } else {
      setShowAuthWarning(false);
    }
  }, [authState.isAuthenticated, authState.isLoading]);

  const handleScrapeSubmit = async (url: string, enableAI: boolean) => {
    // If not authenticated, redirect to login
    if (!authState.isAuthenticated) {
      router.push('/login?redirect=/scraper');
      return;
    }
    
    setEnableAI(enableAI);
    await scrapeWebsite(url, enableAI);
  };
  
  const handleReset = () => {
    resetResult();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Website Scraper</h1>
        <div className="flex items-center space-x-4">
          <Link 
            href="/scraper/history"
            className="text-primary-600 hover:text-primary-700 flex items-center text-sm"
          >
            <IconHistory className="h-5 w-5 mr-1" />
            View History
          </Link>
          <ScraperHealthIndicator />
        </div>
      </div>

      {showAuthWarning && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded mb-6" role="alert">
          <div className="flex">
            <div>
              <p className="font-bold">Authentication Required</p>
              <p className="text-sm">You need to be logged in to use the scraper. Please 
                <button 
                  onClick={() => router.push('/login?redirect=/scraper')} 
                  className="font-bold underline ml-1"
                >
                  login
                </button> or 
                <button 
                  onClick={() => router.push('/signup?redirect=/scraper')} 
                  className="font-bold underline ml-1"
                >
                  create an account
                </button>.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <ScraperForm onSubmit={handleScrapeSubmit} isLoading={isLoading} onReset={handleReset} />
      </div>
      
      {/* Show billing feedback when authenticated */}
      {authState.isAuthenticated && (
        <BillingFeedback 
          creditsUsed={result?.meta?.billing?.apiCall} 
          isAIEnabled={enableAI} 
        />
      )}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
          <div className="flex items-start">
            <div className="flex-grow">
              <p className="font-medium">Error:</p>
              <p>{error}</p>
              
              {/* Credit-related error handling */}
              {error.toLowerCase().includes('credit') && (
                <div className="mt-2">
                  <p className="text-sm">You need more credits to use this feature. Please check your account balance.</p>
                  <button 
                    onClick={() => router.push('/settings/billing')} 
                    className="mt-2 bg-red-600 hover:bg-red-700 text-white py-1 px-3 text-sm rounded"
                  >
                    Purchase Credits
                  </button>
                </div>
              )}
              
              {/* Authentication error handling */}
              {(error.toLowerCase().includes('token') || error.toLowerCase().includes('auth') || error.toLowerCase().includes('permission')) && (
                <div className="mt-2">
                  <p className="text-sm">There might be an issue with your session. Please try logging in again.</p>
                  <button 
                    onClick={() => router.push('/login?redirect=/scraper')} 
                    className="mt-2 bg-red-600 hover:bg-red-700 text-white py-1 px-3 text-sm rounded"
                  >
                    Login Again
                  </button>
                </div>
              )}
            </div>
            <button 
              onClick={handleReset} 
              className="text-red-700 hover:text-red-800"
            >
              ×
            </button>
          </div>
        </div>
      )}
      
      {result && !error && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <ScraperResults result={result} />
        </div>
      )}
    </div>
  );
};

export default ScraperContainer;
