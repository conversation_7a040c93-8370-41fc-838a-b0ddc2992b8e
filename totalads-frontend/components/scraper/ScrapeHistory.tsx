"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/context/AuthContext';
import { getScrapeHistory, cancelScrapeJob } from '@/utils/api/scraperClient';
import { ScrapeHistoryItem } from './utils/scraperTypes';
import { format, formatDistanceToNow } from 'date-fns';
import { IconRefresh, IconEye, IconX } from '@tabler/icons-react';

const ScrapeHistory: React.FC = () => {
  const [history, setHistory] = useState<ScrapeHistoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [cancelingJobId, setCancelingJobId] = useState<string | null>(null);
  
  const router = useRouter();
  const { state } = useAuthContext();
  const { isAuthenticated } = state;

  useEffect(() => {
    if (isAuthenticated) {
      fetchHistory();
    }
  }, [isAuthenticated, page]);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getScrapeHistory(page, 10);
      setHistory(response.data);
      setTotalPages(Math.ceil(response.total / response.limit));
    } catch (err) {
      setError('Failed to load scrape history. Please try again later.');
      console.error('Error fetching scrape history:', err);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  const handleCancelJob = async (jobId: string) => {
    try {
      setCancelingJobId(jobId);
      const result = await cancelScrapeJob(jobId);
      if (result.success) {
        // Update the job status in the local state
        setHistory(currentHistory => 
          currentHistory.map(item => 
            item.id === jobId ? { ...item, status: 'canceled' } : item
          )
        );
      } else {
        setError(`Failed to cancel job: ${result.message}`);
      }
    } catch (err: any) {
      setError(`Error canceling job: ${err.message}`);
    } finally {
      setCancelingJobId(null);
    }
  };

  const handleViewResult = (jobId: string) => {
    router.push(`/scraper/results/${jobId}`);
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'completed':
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>Completed</span>;
      case 'processing':
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>Processing</span>;
      case 'failed':
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>Failed</span>;
      case 'canceled':
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>Canceled</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{status}</span>;
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 my-4">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              You need to be logged in to view your scrape history.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (loading && !isLoadingMore) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-bg-200 rounded-lg shadow-sm">
      <div className="p-4 border-b border-bg-200 flex justify-between items-center">
        <h3 className="text-lg font-medium">Recent Scrapes</h3>
        <button 
          onClick={fetchHistory}
          className="text-sm text-primary-600 hover:text-primary-700 flex items-center"
          disabled={loading}
        >
          <IconRefresh className="w-4 h-4 mr-1" />
          Refresh
        </button>
      </div>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 m-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {history.length === 0 && !loading ? (
        <div className="p-8 text-center text-text-200">
          <p>No scrape history found. Try scraping a URL first.</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-bg-200">
              <thead className="bg-bg-50">
                <tr>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-text-300 uppercase tracking-wider">URL</th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-text-300 uppercase tracking-wider">Date</th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-text-300 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-text-300 uppercase tracking-wider">AI</th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-text-300 uppercase tracking-wider">Credits</th>
                  <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-text-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-bg-200">
                {history.map((item) => (
                  <tr key={item.id} className="hover:bg-bg-50">
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <div className="max-w-xs truncate">{item.url}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-text-300">
                      <div title={format(new Date(item.createdAt), 'PPpp')}>
                        {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      {item.enabledAI ? 
                        <span className="text-green-600">Yes</span> : 
                        <span className="text-text-300">No</span>}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      {item.creditsUsed}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {item.status === 'completed' && (
                          <button
                            onClick={() => handleViewResult(item.id)}
                            className="text-primary-600 hover:text-primary-700"
                            title="View Result"
                          >
                            <IconEye className="w-5 h-5" />
                          </button>
                        )}
                        {item.status === 'processing' && (
                          <button
                            onClick={() => handleCancelJob(item.id)}
                            className="text-red-600 hover:text-red-700"
                            title="Cancel Job"
                            disabled={cancelingJobId === item.id}
                          >
                            {cancelingJobId === item.id ? 
                              <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-red-600"></div> : 
                              <IconX className="w-5 h-5" />
                            }
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-bg-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  disabled={page === 1 || loading}
                  className={`relative inline-flex items-center px-4 py-2 border border-bg-300 text-sm font-medium rounded-md ${
                    page === 1 ? 'text-text-200 bg-bg-50' : 'text-text-400 bg-white hover:bg-bg-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages || loading}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-bg-300 text-sm font-medium rounded-md ${
                    page === totalPages ? 'text-text-200 bg-bg-50' : 'text-text-400 bg-white hover:bg-bg-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-text-400">
                    Showing <span className="font-medium">page {page}</span> of{" "}
                    <span className="font-medium">{totalPages}</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      onClick={() => setPage(1)}
                      disabled={page === 1 || loading}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-bg-300 bg-white text-sm font-medium ${
                        page === 1 ? 'text-text-200' : 'text-text-400 hover:bg-bg-50'
                      }`}
                    >
                      <span className="sr-only">First</span>
                      <span>«</span>
                    </button>
                    <button
                      onClick={() => setPage(p => Math.max(1, p - 1))}
                      disabled={page === 1 || loading}
                      className={`relative inline-flex items-center px-2 py-2 border border-bg-300 bg-white text-sm font-medium ${
                        page === 1 ? 'text-text-200' : 'text-text-400 hover:bg-bg-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <span>‹</span>
                    </button>
                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setPage(pageNum)}
                          disabled={loading}
                          className={`relative inline-flex items-center px-4 py-2 border ${
                            page === pageNum
                              ? 'bg-primary-50 border-primary-500 text-primary-600 z-10'
                              : 'border-bg-300 bg-white text-text-400 hover:bg-bg-50'
                          } text-sm font-medium`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                      disabled={page === totalPages || loading}
                      className={`relative inline-flex items-center px-2 py-2 border border-bg-300 bg-white text-sm font-medium ${
                        page === totalPages ? 'text-text-200' : 'text-text-400 hover:bg-bg-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <span>›</span>
                    </button>
                    <button
                      onClick={() => setPage(totalPages)}
                      disabled={page === totalPages || loading}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-bg-300 bg-white text-sm font-medium ${
                        page === totalPages ? 'text-text-200' : 'text-text-400 hover:bg-bg-50'
                      }`}
                    >
                      <span className="sr-only">Last</span>
                      <span>»</span>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ScrapeHistory;
