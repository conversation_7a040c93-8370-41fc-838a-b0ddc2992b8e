{"name": "totalads-dev", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@tabler/icons-react": "^3.7.0", "@tanstack/react-table": "^8.20.1", "@types/tailwindcss": "^3.1.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flowbite": "^2.4.1", "framer-motion": "^11.3.27", "lucide-react": "^0.399.0", "mini-svg-data-uri": "^1.4.4", "next": "14.2.4", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-lottie": "^1.2.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "totalads-dev": "file:"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lottie": "^1.2.10", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}