"use client";

// Key for storing the token in localStorage/sessionStorage
const TOKEN_KEY = 'totalads_auth_token';

/**
 * Utility for handling token storage
 * Provides methods to store, retrieve, and remove the authentication token
 */
export const tokenStorage = {
  /**
   * Store authentication token
   * @param token Token to store
   * @param rememberMe Whether to store in localStorage (persistent) or sessionStorage (session only)
   */
  setToken(token: string, rememberMe: boolean = true): void {
    try {
      if (typeof window !== 'undefined') {
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem(TOKEN_KEY, token);
      }
    } catch (error) {
      console.error('Error storing token:', error);
    }
  },

  /**
   * Get stored authentication token
   * @returns The stored token or null if not found
   */
  getToken(): string | null {
    if (typeof window === 'undefined') {
      return null;
    }

    try {
      // Try to get token from sessionStorage first
      let token = sessionStorage.getItem(TOKEN_KEY);

      // If not found in sessionStorage, try localStorage
      if (!token) {
        token = localStorage.getItem(TOKEN_KEY);
      }

      return token;
    } catch (error) {
      console.error('Error retrieving token:', error);
      return null;
    }
  },

  /**
   * Remove stored authentication token
   */
  removeToken(): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // Remove from both storages to ensure it's completely cleared
      localStorage.removeItem(TOKEN_KEY);
      sessionStorage.removeItem(TOKEN_KEY);
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }
};
