"use client";

import axios from 'axios';

import { tokenStorage } from '../auth/tokenStorage';

// API base URLs
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Enable cookies for all requests
});

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // If 401 Unauthorized, redirect to login page
    if (error.response?.status === 401) {
      // If in browser context, redirect to login page
      if (typeof window !== "undefined") {
        // Check if we're not already on the login page
        if (!window.location.pathname.includes("/login")) {
          window.location.href = "/login";
        }
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
