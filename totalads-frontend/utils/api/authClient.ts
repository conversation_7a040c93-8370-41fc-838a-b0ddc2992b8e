"use client";

import axios from 'axios';

import { tokenStorage } from '../auth/tokenStorage';
import apiClient from './apiClient';

/**
 * Type definitions for authentication
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
}

export interface AuthResponse {
  token: string;
  user: UserProfile;
}

/**
 * Login user with email and password
 */
export const login = async (
  credentials: LoginCredentials
): Promise<AuthResponse> => {
  try {
    const response = await apiClient.post("/auth/login", credentials, {
      withCredentials: true, // Enable cookies
    });

    // API returns success message, we need to get user data separately
    const userResponse = await getCurrentUser();

    return {
      token: "", // Not used with cookie auth
      user: userResponse,
    };
  } catch (error: unknown) {
    console.error("Login error:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Authentication failed: ${error.response.status}`
      );
    }
    throw error;
  }
};

/**
 * Register a new user
 */
export const register = async (
  credentials: RegisterCredentials
): Promise<AuthResponse> => {
  try {
    const response = await apiClient.post("/auth/signup", credentials, {
      withCredentials: true, // Enable cookies
    });

    // API returns success message, we need to get user data separately
    const userResponse = await getCurrentUser();

    return {
      token: "", // Not used with cookie auth
      user: userResponse,
    };
  } catch (error: unknown) {
    console.error("Registration error:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Registration failed: ${error.response.status}`
      );
    }
    throw error;
  }
};

/**
 * Get current user profile
 */
export const getCurrentUser = async (): Promise<UserProfile> => {
  try {
    const response = await apiClient.get("/users/me", {
      withCredentials: true, // Enable cookies
    });
    return response.data;
  } catch (error: unknown) {
    console.error("Error getting current user:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Failed to get user profile: ${error.response.status}`
      );
    }
    throw error;
  }
};

/**
 * Logout the current user
 */
export const logout = async (): Promise<void> => {
  try {
    await apiClient.delete("/auth/logout", {
      withCredentials: true, // Enable cookies
    });
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    // Always redirect to login page regardless of API call result
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    await getCurrentUser();
    return true;
  } catch {
    return false;
  }
};

/**
 * Request password reset
 */
export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    await apiClient.post("/api/auth/forgot-password", { email });
  } catch (error: unknown) {
    console.error("Error requesting password reset:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Failed to request password reset: ${error.response.status}`
      );
    }
    throw error;
  }
};

/**
 * Reset password with token
 */
export const resetPassword = async (
  token: string,
  password: string,
  confirmPassword: string
): Promise<void> => {
  try {
    await apiClient.post("/api/auth/reset-password", {
      token,
      password,
      confirmPassword,
    });
  } catch (error: unknown) {
    console.error("Error resetting password:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Failed to reset password: ${error.response.status}`
      );
    }
    throw error;
  }
};
