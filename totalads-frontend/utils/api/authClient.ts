"use client";

import axios from 'axios';

import { tokenStorage } from '../auth/tokenStorage';
import apiClient from './apiClient';

/**
 * Type definitions for authentication
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  userType: string;
  emailVerified: boolean;
}

export interface AuthResponse {
  token: string;
  user: UserProfile;
}

/**
 * Login user with email and password
 */
export const login = async (
  credentials: LoginCredentials
): Promise<AuthResponse> => {
  try {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Authentication failed");
    }

    return {
      token: "", // Not used with cookie auth
      user: data.user,
    };
  } catch (error: unknown) {
    console.error("Login error:", error);
    throw error;
  }
};

/**
 * Register a new user
 */
export const register = async (
  credentials: RegisterCredentials
): Promise<AuthResponse> => {
  try {
    const response = await fetch("/api/auth/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Registration failed");
    }

    return {
      token: "", // Not used with cookie auth
      user: data.user,
    };
  } catch (error: unknown) {
    console.error("Registration error:", error);
    throw error;
  }
};

/**
 * Get current user profile
 */
export const getCurrentUser = async (): Promise<UserProfile> => {
  try {
    const response = await fetch("/api/auth/me", {
      method: "GET",
      credentials: "include", // Include cookies
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to get user");
    }

    return data.user;
  } catch (error: unknown) {
    console.error("Error getting current user:", error);
    throw error;
  }
};

/**
 * Logout the current user
 */
export const logout = async (): Promise<void> => {
  try {
    await fetch("/api/auth/logout", {
      method: "DELETE",
      credentials: "include", // Include cookies
    });
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    // Always redirect to login page regardless of API call result
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    await getCurrentUser();
    return true;
  } catch {
    return false;
  }
};

/**
 * Request password reset
 */
export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    await apiClient.post("/api/auth/forgot-password", { email });
  } catch (error: unknown) {
    console.error("Error requesting password reset:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Failed to request password reset: ${error.response.status}`
      );
    }
    throw error;
  }
};

/**
 * Reset password with token
 */
export const resetPassword = async (
  token: string,
  password: string,
  confirmPassword: string
): Promise<void> => {
  try {
    await apiClient.post("/api/auth/reset-password", {
      token,
      password,
      confirmPassword,
    });
  } catch (error: unknown) {
    console.error("Error resetting password:", error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(
        error.response.data?.error ||
          `Failed to reset password: ${error.response.status}`
      );
    }
    throw error;
  }
};
