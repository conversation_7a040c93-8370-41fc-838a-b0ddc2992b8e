"use client";

export interface ApiToken {
  id: string;
  name: string;
  token: string; // Only returned when a new token is generated
  prefix: string;
  createdAt: string;
  lastUsedAt?: string;
  expiresAt?: string;
  usageCount: number;
  scopes: string[];
  permissions?: string[];
}

export interface TokenCreateParams {
  name: string;
  expiresIn?: number; // expiration in days, optional
  scopes?: string[];
  permissions?: string[];
}

export interface TokenUsageStats {
  totalRequests: number;
  lastUsed?: string;
  dailyUsage: { date: string; count: number }[];
  monthlyUsage: { month: string; count: number }[];
}

/**
 * Service for managing API tokens for third-party access
 */
export const tokenService = {
  /**
   * Get all API tokens for current user
   * @returns List of API tokens
   */
  async getTokens(): Promise<ApiToken[]> {
    try {
      const response = await fetch('/api/tokens', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch API tokens');
      }

      const data = await response.json();
      return data.tokens;
    } catch (error) {
      console.error('Error fetching API tokens:', error);
      throw error;
    }
  },

  /**
   * Create a new API token
   * @param params Token creation parameters
   * @returns The newly created token
   */
  async createToken(params: TokenCreateParams): Promise<ApiToken> {
    try {
      const response = await fetch('/api/tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create API token');
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error('Error creating API token:', error);
      throw error;
    }
  },

  /**
   * Delete an API token
   * @param tokenId ID of the token to delete
   * @returns Success status
   */
  async deleteToken(tokenId: string): Promise<{ success: boolean }> {
    try {
      const response = await fetch(`/api/tokens/${tokenId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete API token');
      }

      const data = await response.json();
      return { success: data.success };
    } catch (error) {
      console.error('Error deleting API token:', error);
      throw error;
    }
  },

  /**
   * Get usage statistics for an API token
   * @param tokenId ID of the token to get stats for
   * @returns Token usage statistics
   */
  async getTokenUsage(tokenId: string): Promise<TokenUsageStats> {
    try {
      const response = await fetch(`/api/tokens/${tokenId}/usage`, {
        method: 'GET',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch token usage');
      }

      const data = await response.json();
      return data.usage;
    } catch (error) {
      console.error('Error fetching token usage:', error);
      throw error;
    }
  },

  /**
   * Update an API token's name
   * @param tokenId ID of the token to update
   * @param name New name for the token
   * @returns Updated token
   */
  async updateTokenName(tokenId: string, name: string): Promise<ApiToken> {
    try {
      const response = await fetch(`/api/tokens/${tokenId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update API token');
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error('Error updating API token:', error);
      throw error;
    }
  }
};
