/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Enhanced scraper service with intelligent queuing and concurrency management
 */
import { scraperService } from "../core/scraper.service";
import logger from "../utils/logger";

export interface ScrapeJobData {
	url: string;
	enableAI?: boolean;
	requestId: string;
	clientIp?: string;
	priority?: number;
	metadata?: Record<string, any>;
}

export interface ScrapeJobResult {
	url: string;
	success: boolean;
	data?: any;
	error?: string;
	processingTime: number;
	timestamp: number;
	requestId: string;
}

class EnhancedScraperService {
	private activeRequests = new Map<string, Promise<any>>();
	private requestQueue: Array<{
		data: ScrapeJobData;
		resolve: (value: ScrapeJobResult) => void;
		reject: (reason: any) => void;
	}> = [];
	private isProcessingQueue = false;

	constructor() {
		// Start queue processor
		this.startQueueProcessor();
	}

	/**
	 * Main scraping method with intelligent routing
	 */
	async scrapeUrl(data: ScrapeJobData): Promise<ScrapeJobResult> {
		const { url, requestId } = data;
		const startTime = Date.now();

		try {
			// Check if we should use direct processing or queue
			const shouldQueue = await this.shouldUseQueue();

			if (shouldQueue) {
				logger.info(
					`Queuing scrape request for ${url} (requestId: ${requestId})`,
				);
				return await this.queueScrapeRequest(data);
			} else {
				logger.info(
					`Processing scrape request directly for ${url} (requestId: ${requestId})`,
				);
				return await this.processDirectly(data);
			}
		} catch (error) {
			const processingTime = Date.now() - startTime;
			logger.error(
				`Scrape request failed for ${url} (requestId: ${requestId}):`,
				error,
			);

			return {
				url,
				success: false,
				error: error instanceof Error ? error.message : "Unknown error",
				processingTime,
				timestamp: Date.now(),
				requestId,
			};
		}
	}

	/**
	 * Process scrape request directly (for low load)
	 */
	private async processDirectly(
		data: ScrapeJobData,
	): Promise<ScrapeJobResult> {
		const { url, requestId } = data;
		const startTime = Date.now();

		// Check if this URL is already being processed
		if (this.activeRequests.has(url)) {
			logger.info(
				`URL ${url} already being processed, waiting for result`,
			);
			try {
				const existingResult = await this.activeRequests.get(url);
				return {
					...existingResult,
					requestId,
					timestamp: Date.now(),
				};
			} catch (error) {
				console.log("error: ", error);
				// If existing request failed, continue with new request
				this.activeRequests.delete(url);
			}
		}

		// Create new processing promise
		const processingPromise = this.executeScrapingLogic(data);
		this.activeRequests.set(url, processingPromise);

		try {
			const result = await processingPromise;
			this.activeRequests.delete(url);

			const processingTime = Date.now() - startTime;
			return {
				url,
				success: true,
				data: result,
				processingTime,
				timestamp: Date.now(),
				requestId,
			};
		} catch (error) {
			this.activeRequests.delete(url);
			throw error;
		}
	}

	/**
	 * Queue scrape request (for high load)
	 */
	private async queueScrapeRequest(
		data: ScrapeJobData,
	): Promise<ScrapeJobResult> {
		return new Promise((resolve, reject) => {
			// Add to internal queue first for immediate processing if possible
			this.requestQueue.push({ data, resolve, reject });
			this.processQueueIfNeeded();
		});
	}

	/**
	 * Execute the actual scraping logic
	 */
	private async executeScrapingLogic(data: ScrapeJobData): Promise<any> {
		const { url, enableAI = false } = data;

		// Step 1: Execute normal scraping
		const basicResult = await scraperService.scrape(url);

		// Step 2: Optionally enhance with AI
		if (enableAI) {
			try {
				// Note: AI enhancement would be implemented here
				// For now, we'll use the basic result
				logger.debug(
					`AI enhancement requested for ${url} but not implemented yet`,
				);
			} catch (aiError) {
				logger.warn(
					`AI enhancement failed for ${url}, using basic result:`,
					aiError,
				);
			}
		}

		return basicResult;
	}

	/**
	 * Determine if we should use queue based on current load
	 */
	private async shouldUseQueue(): Promise<boolean> {
		const activeCount = this.activeRequests.size;
		const maxConcurrent = parseInt(
			process.env.SCRAPER_MAX_CONCURRENT_REQUESTS || "50",
		);
		const queueThreshold = parseInt(
			process.env.SCRAPER_QUEUE_THRESHOLD || "30",
		);

		// Use queue if we're approaching max concurrent requests
		if (activeCount >= queueThreshold) {
			logger.debug(
				`Using queue due to high load: ${activeCount}/${maxConcurrent} active requests`,
			);
			return true;
		}

		return false;
	}

	/**
	 * Process internal queue
	 */
	private async processQueueIfNeeded(): Promise<void> {
		if (this.isProcessingQueue || this.requestQueue.length === 0) {
			return;
		}

		this.isProcessingQueue = true;

		try {
			while (this.requestQueue.length > 0) {
				const activeCount = this.activeRequests.size;
				const maxConcurrent = parseInt(
					process.env.SCRAPER_CONCURRENCY || "10",
				);

				if (activeCount >= maxConcurrent) {
					// Wait a bit before checking again
					await new Promise((resolve) => setTimeout(resolve, 100));
					continue;
				}

				const queueItem = this.requestQueue.shift();
				if (!queueItem) break;

				// Process the request
				this.processDirectly(queueItem.data)
					.then(queueItem.resolve)
					.catch(queueItem.reject);
			}
		} finally {
			this.isProcessingQueue = false;
		}
	}

	/**
	 * Start background queue processor
	 */
	private startQueueProcessor(): void {
		setInterval(() => {
			this.processQueueIfNeeded();
		}, 1000); // Check every second
	}

	/**
	 * Get service statistics
	 */
	async getStats(): Promise<any> {
		return {
			activeRequests: this.activeRequests.size,
			internalQueue: this.requestQueue.length,
			configuration: {
				maxConcurrent:
					process.env.SCRAPER_MAX_CONCURRENT_REQUESTS || "50",
				queueThreshold: process.env.SCRAPER_QUEUE_THRESHOLD || "30",
				concurrency: process.env.SCRAPER_CONCURRENCY || "10",
			},
		};
	}

	/**
	 * Health check
	 */
	async healthCheck(): Promise<{ healthy: boolean; stats: any }> {
		const stats = await this.getStats();
		const healthy =
			stats.activeRequests <
			parseInt(process.env.SCRAPER_MAX_CONCURRENT_REQUESTS || "50");

		return { healthy, stats };
	}
}

// Export singleton instance
export const enhancedScraperService = new EnhancedScraperService();
export default enhancedScraperService;
