{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.048Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751471993364_igi5tol2s\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T15:59:53.364Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:03.848Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472004108_uu6aafvqx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:04.108Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.507Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472042727_oj13cd8ms\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:42.727Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.199Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472056470_tgy47y712\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:56.470Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:09.887Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472070081_eo4kqielp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:10.081Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.446Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472089658_ae8gpdqwm\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:29.658Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:44.286Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473544567_yu3pt0h4i\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:25:44.567Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:44.607Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:25:44.607Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:56.757Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473556961_tw8l7pe8w\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:25:56.961Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:56.994Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:25:56.994Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:26:21.361Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473581595_n5c1y63fx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:26:21.595Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:26:21.645Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:26:21.645Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:32:01.414Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473921670_nw22z2yy5\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:32:01.670Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:32:01.700Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:32:01.700Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:25.186Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474005358_u8vjj45b9\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:33:25.358Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:25.384Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:33:25.384Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:48.378Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474028550_zapiavmk1\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:33:48.550Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:48.575Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:33:48.575Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:10.144Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474350344_mrih0aqom\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:39:10.344Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:10.373Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:39:10.373Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:38.569Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474378755_i05djtjdy\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:39:38.755Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:38.783Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:39:38.783Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:40:00.589Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474400886_73uqhs3f4\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:40:00.886Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:40:00.930Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:40:00.931Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:37.960Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474498269_k9kjrd20v\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:41:38.269Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:38.299Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:41:38.299Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:57.153Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474517337_q9kltpiaq\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:41:57.337Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:57.399Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:41:57.399Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:42:12.646Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474532827_q7j7c4x6p\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:42:12.827Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:42:12.853Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:42:12.853Z"}
{"clientIp":"::1","enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","requestId":"86e699a7-db11-41d9-a071-322a4fef06f4","timestamp":"2025-07-02T16:45:25.246Z"}
{"level":"info","message":"Processing scrape request directly for https://www.ezrankings.com/ (requestId: 86e699a7-db11-41d9-a071-322a4fef06f4)","timestamp":"2025-07-02T16:45:25.247Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-07-02T16:45:25.247Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-07-02T16:45:25.251Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-07-02T16:45:26.565Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com (large website: false)","timestamp":"2025-07-02T16:45:26.566Z"}
{"level":"info","message":"Attempting scrape-https://www.ezrankings.com (attempt 1/4)","timestamp":"2025-07-02T16:45:26.566Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-07-02T16:45:27.751Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-07-02T16:45:27.751Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-07-02T16:45:27.756Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-07-02T16:45:27.758Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-07-02T16:45:27.760Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-07-02T16:45:27.761Z"}
{"level":"info","message":"Converted 32255 characters of HTML to text","timestamp":"2025-07-02T16:45:27.853Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-07-02T16:45:27.854Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-07-02T16:45:27.855Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.ezrankings.com","timestamp":"2025-07-02T16:45:27.856Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com","timestamp":"2025-07-02T16:45:27.858Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:45:27.859Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:45:27.861Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-02T16:45:27.867Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-07-02T16:45:27.867Z"}
{"level":"info","message":"Finding about and team pages for https://www.ezrankings.com","timestamp":"2025-07-02T16:45:27.867Z"}
{"level":"info","message":"Found 14 potential about/team pages","timestamp":"2025-07-02T16:45:27.869Z"}
{"level":"info","message":"Found 14 about/team pages to extract","timestamp":"2025-07-02T16:45:27.869Z"}
{"level":"info","message":"Extracting enhanced about data from 14 pages","timestamp":"2025-07-02T16:45:27.869Z"}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/about-us.html","timestamp":"2025-07-02T16:45:27.869Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/about-us.html","timestamp":"2025-07-02T16:45:30.388Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:45:30.391Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:45:30.392Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":3,"timestamp":"2025-07-02T16:45:30.396Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-02T16:45:30.397Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-02T16:45:31.666Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:45:31.667Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:45:31.669Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-02T16:45:31.702Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6484ms","timestamp":"2025-07-02T16:45:31.731Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:05.470Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475245698_gepertax2\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:54:05.698Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:05.727Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:54:05.727Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:29.482Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475269710_59ksbkrg1\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:54:29.710Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:29.746Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:54:29.746Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:56.033Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475296222_rqnxbr8c0\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:54:56.222Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:54:56.252Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:54:56.253Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:55:36.451Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475336656_906s2oh90\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:55:36.656Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:55:36.691Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:55:36.691Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:04.134Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475364342_dnw73ij56\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:56:04.342Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:04.369Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:56:04.369Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:15.530Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475375727_zml5ylax9\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:56:15.727Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:15.757Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:56:15.757Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:28.437Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475388633_3q87z89jq\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:56:28.633Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:28.669Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:56:28.669Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:41.171Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751475401360_ghecgckyx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:56:41.360Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:56:41.390Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:56:41.390Z"}
{"clientIp":"::1","enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","requestId":"fa2ea79a-b62c-4415-9b6e-2b9dbf2447e7","timestamp":"2025-07-02T16:59:51.017Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-07-02T16:59:51.017Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-07-02T16:59:51.020Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-07-02T16:59:51.657Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com (large website: false)","timestamp":"2025-07-02T16:59:51.658Z"}
{"level":"info","message":"Attempting scrape-https://www.ezrankings.com (attempt 1/4)","timestamp":"2025-07-02T16:59:51.658Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-07-02T16:59:52.804Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-07-02T16:59:52.805Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-07-02T16:59:52.809Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-07-02T16:59:52.815Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-07-02T16:59:52.816Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-07-02T16:59:52.816Z"}
{"level":"info","message":"Converted 32255 characters of HTML to text","timestamp":"2025-07-02T16:59:52.858Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-07-02T16:59:52.858Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-07-02T16:59:52.862Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.ezrankings.com","timestamp":"2025-07-02T16:59:52.864Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com","timestamp":"2025-07-02T16:59:52.864Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:59:52.864Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:59:52.865Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-02T16:59:52.869Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-07-02T16:59:52.870Z"}
{"level":"info","message":"Finding about and team pages for https://www.ezrankings.com","timestamp":"2025-07-02T16:59:52.870Z"}
{"level":"info","message":"Found 14 potential about/team pages","timestamp":"2025-07-02T16:59:52.871Z"}
{"level":"info","message":"Found 14 about/team pages to extract","timestamp":"2025-07-02T16:59:52.871Z"}
{"level":"info","message":"Extracting enhanced about data from 14 pages","timestamp":"2025-07-02T16:59:52.871Z"}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/about-us.html","timestamp":"2025-07-02T16:59:52.872Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/about-us.html","timestamp":"2025-07-02T16:59:55.006Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:59:55.077Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:59:55.080Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":3,"timestamp":"2025-07-02T16:59:55.093Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-02T16:59:55.094Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-02T16:59:56.724Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-02T16:59:56.725Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-02T16:59:56.735Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-02T16:59:56.737Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5737ms","timestamp":"2025-07-02T16:59:56.754Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:13:10.916Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751476391114_8q6rrjw1x\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T17:13:11.114Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:13:11.140Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T17:13:11.140Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:13:43.260Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751476423456_u1idb2u1n\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T17:13:43.456Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:13:43.490Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T17:13:43.490Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:15:24.537Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751476524825_tb01q7eq9\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T17:15:24.825Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:15:24.859Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T17:15:24.859Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:15:31.684Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751476531872_m6w7cr1wa\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T17:15:31.872Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T17:15:31.897Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T17:15:31.897Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-04T03:26:54.146Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751599614349_lwl0l2oz7\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-04T03:26:54.349Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-04T03:26:54.379Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-04T03:26:54.380Z"}
{"clientIp":"::1","enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","requestId":"188593d5-8c93-49c1-aa47-14ee91f6e04e","timestamp":"2025-07-04T03:27:00.241Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-07-04T03:27:00.241Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-07-04T03:27:00.242Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-07-04T03:27:00.908Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com (large website: false)","timestamp":"2025-07-04T03:27:00.908Z"}
{"level":"info","message":"Attempting scrape-https://www.ezrankings.com (attempt 1/4)","timestamp":"2025-07-04T03:27:00.908Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-07-04T03:27:11.168Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-07-04T03:27:11.168Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-07-04T03:27:11.173Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-07-04T03:27:11.173Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-07-04T03:27:11.176Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-07-04T03:27:11.177Z"}
{"level":"info","message":"Converted 32255 characters of HTML to text","timestamp":"2025-07-04T03:27:11.215Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-07-04T03:27:11.215Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-07-04T03:27:11.216Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.ezrankings.com","timestamp":"2025-07-04T03:27:11.217Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com","timestamp":"2025-07-04T03:27:11.217Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-04T03:27:11.217Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-04T03:27:11.217Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-04T03:27:11.221Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-07-04T03:27:11.221Z"}
{"level":"info","message":"Finding about and team pages for https://www.ezrankings.com","timestamp":"2025-07-04T03:27:11.221Z"}
{"level":"info","message":"Found 14 potential about/team pages","timestamp":"2025-07-04T03:27:11.222Z"}
{"level":"info","message":"Found 14 about/team pages to extract","timestamp":"2025-07-04T03:27:11.222Z"}
{"level":"info","message":"Extracting enhanced about data from 14 pages","timestamp":"2025-07-04T03:27:11.222Z"}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/about-us.html","timestamp":"2025-07-04T03:27:11.222Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/about-us.html","timestamp":"2025-07-04T03:27:13.506Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-04T03:27:13.513Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-04T03:27:13.515Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":true,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":3,"timestamp":"2025-07-04T03:27:13.519Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-04T03:27:13.520Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-07-04T03:27:14.938Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-07-04T03:27:14.938Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-07-04T03:27:14.944Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-07-04T03:27:14.954Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 14726ms","timestamp":"2025-07-04T03:27:14.967Z"}
