// Test script to verify authentication flow
const API_BASE = 'http://localhost:3000';

async function testSignup() {
  console.log('Testing signup...');
  
  const response = await fetch(`${API_BASE}/api/auth/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    }),
  });

  const data = await response.json();
  console.log('Signup response:', response.status, data);
  
  return response.ok;
}

async function testLogin() {
  console.log('Testing login...');
  
  const response = await fetch(`${API_BASE}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123',
    }),
  });

  const data = await response.json();
  console.log('Login response:', response.status, data);
  
  return response.ok;
}

async function testGetUser() {
  console.log('Testing get user...');
  
  const response = await fetch(`${API_BASE}/api/auth/me`, {
    method: 'GET',
    credentials: 'include',
  });

  const data = await response.json();
  console.log('Get user response:', response.status, data);
  
  return response.ok;
}

async function runTests() {
  try {
    await testSignup();
    await testLogin();
    await testGetUser();
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run tests if this is executed directly
if (typeof window === 'undefined') {
  runTests();
}
