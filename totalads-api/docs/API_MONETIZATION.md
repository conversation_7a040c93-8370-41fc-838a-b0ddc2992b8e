# TotalAds API Monetization System

This document explains how the API monetization system works for the TotalAds scraper service.

## Overview

The API monetization system allows users to access the scraper service via the API with a pricing model of $0.05 per API call after 10 free calls per month. The system includes:

1. API token management
2. Usage tracking
3. Billing calculation
4. Access control to the scraper service

## API Routes

### API Management Routes (`/api-management`)

These routes are for users to manage their API tokens and view usage/billing information:

#### API Token Management

- `POST /api-management/tokens` - Generate a new API token
  - Request body: `{ "name": "My API Token" }`
  - Response: `{ "success": true, "data": { "id": "123", "name": "My API Token", "token": "ta_abcd1234...", ... } }`

- `GET /api-management/tokens` - List all API tokens for the user
  - Response: `{ "success": true, "data": [{ "id": "123", "name": "My API Token", "token": "••••••••abcd1234", ... }] }`

- `DELETE /api-management/tokens/:tokenId` - Revoke an API token
  - Response: `{ "success": true, "message": "API token revoked successfully" }`

#### Usage & Billing Information

- `GET /api-management/usage?month=2025-07` - Get API usage statistics (defaults to current month)
  - Response: 
  ```json
  {
    "success": true,
    "data": {
      "total": 15,
      "free": 10,
      "billable": 5,
      "byEndpoint": { "/scrape": 15 },
      "byToken": { "My API Token": 15 }
    }
  }
  ```

- `GET /api-management/billing?month=2025-07` - Get billing information (defaults to current month)
  - Response: 
  ```json
  {
    "success": true,
    "data": {
      "totalCalls": 15,
      "freeCalls": 10,
      "billableCalls": 5,
      "rate": 0.05,
      "totalAmount": 0.25,
      "month": "2025-07"
    }
  }
  ```

### Scraper API Gateway (`/api/scraper`)

These endpoints require an API token for authentication. The token must be provided in the Authorization header as a bearer token.

- `POST /api/scraper` - Scrape a URL
  - Request headers: `Authorization: Bearer ta_your_api_token`
  - Request body: `{ "url": "https://example.com", "enableAI": false }`
  - Response: Results from the scraper service with additional billing information

- `GET /api/scraper/health` - Check the health of the scraper service
  - Request headers: `Authorization: Bearer ta_your_api_token`
  - Response: Health status from the scraper service

## Authentication

### Web UI Authentication

For the web UI and API management routes, the system uses cookie-based authentication with the existing auth system.

### API Authentication

For the scraper API gateway, the system uses token-based authentication. Users must include their API token in the Authorization header:

```
Authorization: Bearer ta_your_api_token
```

## Pricing Model

- Free tier: 10 API calls per month
- Paid tier: $0.05 per API call after free tier is used

## Implementation Details

### Database Tables

1. **API Tokens (`api_tokens`)**: Stores API tokens for each user
   - `id`: Primary key
   - `userId`: Foreign key to users table
   - `token`: The API token string
   - `name`: User-defined name for the token
   - `active`: Whether the token is active
   - `createdAt`: When the token was created
   - `lastUsedAt`: When the token was last used
   - `revokedAt`: When the token was revoked (if applicable)

2. **API Usage (`api_usage`)**: Tracks API usage for billing
   - `id`: Primary key
   - `userId`: Foreign key to users table
   - `tokenId`: Foreign key to api_tokens table
   - `endpoint`: The API endpoint called
   - `requestId`: Unique ID for the request
   - `timestamp`: When the call was made
   - `responseStatus`: HTTP status code of the response
   - `charged`: Whether the call was charged (after free tier)
   - `billable`: Whether the call is billable
   - `month`: Month of the call (YYYY-MM format)

## Getting Started

1. Create an API token through the web UI or the API management endpoints
2. Use the token to make requests to the scraper API
3. Monitor usage and billing through the API management endpoints

## Environment Variables

- `SCRAPER_SERVICE_URL`: URL of the scraper service (default: http://localhost:3001)

## Security Considerations

- API tokens are stored securely in the database
- All routes use HTTPS in production
- API tokens are masked in the UI and API responses
- Only active tokens can be used to authenticate API requests
