{"info": {"_postman_id": "3e852c5d-8b8c-4e21-a1c9-c8f06df15e82", "name": "TotalAds API", "description": "Complete API collection for TotalAds application endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8001/health", "protocol": "http", "host": ["localhost"], "port": "8001", "path": ["health"]}, "description": "Simple health check endpoint to verify server is running"}, "response": []}], "description": "Health check endpoints for service status monitoring"}, {"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"yourpassword\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Authenticate user and retrieve access token"}, "response": []}, {"name": "Signup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword\",\n    \"name\": \"New User\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}, "description": "Create a new user account"}, "response": []}, {"name": "Logout", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "Logout the current user and clear authentication cookies"}, "response": []}, {"name": "Send Reset Password Code", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/reset-password/send-code", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password", "send-code"]}, "description": "Send a password reset code to the user's email address"}, "response": []}, {"name": "Validate Reset Password Code", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/reset-password/validate-code?code=123456&email=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password", "validate-code"], "query": [{"key": "code", "value": "123456"}, {"key": "email", "value": "<EMAIL>"}]}, "description": "Validate a password reset code"}, "response": []}, {"name": "Reset Password", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"code\": \"123456\",\n    \"newPassword\": \"newSecurePassword\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/reset-password/reset", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password", "reset"]}, "description": "Reset a user's password with a valid code"}, "response": []}, {"name": "Send Email Verification Code", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/auth/email-verification/send-code", "host": ["{{baseUrl}}"], "path": ["auth", "email-verification", "send-code"]}, "description": "Send an email verification code to the authenticated user"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "PATCH", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/email-verification/verify", "host": ["{{baseUrl}}"], "path": ["auth", "email-verification", "verify"]}, "description": "Verify the email address of an authenticated user with a verification code"}, "response": []}], "description": "Authentication related endpoints including login, signup, password reset and email verification"}, {"name": "Users", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}, "description": "Get details of currently authenticated user"}, "response": []}, {"name": "Update Current User", "request": {"method": "PATCH", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Name\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}, "description": "Update information for the currently authenticated user"}, "response": []}, {"name": "Update User Password", "request": {"method": "PATCH", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"currentSecurePassword\",\n    \"newPassword\": \"newSecurePassword\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/me/password", "host": ["{{baseUrl}}"], "path": ["users", "me", "password"]}, "description": "Update password for the currently authenticated user"}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}, "description": "Delete the currently authenticated user account"}, "response": []}], "description": "User management endpoints"}, {"name": "API Management", "item": [{"name": "Create API Token", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"My API Token\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api-management/tokens", "host": ["{{baseUrl}}"], "path": ["api-management", "tokens"]}, "description": "Create a new API token for the authenticated user"}, "response": []}, {"name": "List API Tokens", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api-management/tokens", "host": ["{{baseUrl}}"], "path": ["api-management", "tokens"]}, "description": "List all API tokens for the authenticated user"}, "response": []}, {"name": "Delete API Token", "request": {"method": "DELETE", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api-management/tokens/:tokenId", "host": ["{{baseUrl}}"], "path": ["api-management", "tokens", ":tokenId"], "variable": [{"key": "tokenId", "value": "123", "description": "ID of the API token to delete"}]}, "description": "Delete an API token by ID"}, "response": []}, {"name": "Get API Usage", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api-management/usage", "host": ["{{baseUrl}}"], "path": ["api-management", "usage"]}, "description": "Get API usage statistics for the authenticated user"}, "response": []}, {"name": "Get API Usage by Token", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api-management/tokens/:tokenId/usage", "host": ["{{baseUrl}}"], "path": ["api-management", "tokens", ":tokenId", "usage"], "variable": [{"key": "tokenId", "value": "123", "description": "ID of the API token to get usage for"}]}, "description": "Get API usage statistics for a specific token"}, "response": []}, {"name": "Get Billing Information", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api-management/billing", "host": ["{{baseUrl}}"], "path": ["api-management", "billing"]}, "description": "Get billing information for the authenticated user"}, "response": []}, {"name": "Update Payment Method", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "auth={{authC<PERSON>ie}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"paymentMethodId\": \"pm_card_visa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api-management/billing/payment-method", "host": ["{{baseUrl}}"], "path": ["api-management", "billing", "payment-method"]}, "description": "Update payment method for the authenticated user"}, "response": []}], "description": "API token management and usage statistics endpoints"}, {"name": "Scraper API", "item": [{"name": "Scrape URL", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"url\": \"https://example.com\",\n    \"enableAI\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/scraper", "host": ["{{baseUrl}}"], "path": ["api", "scraper"]}, "description": "Scrape content from a URL"}, "response": []}, {"name": "Check Scraper Health", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/scraper/health", "host": ["{{baseUrl}}"], "path": ["api", "scraper", "health"]}, "description": "Check health of the scraper service"}, "response": []}, {"name": "Get Scraper Usage", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/scraper/usage", "host": ["{{baseUrl}}"], "path": ["api", "scraper", "usage"]}, "description": "Get usage statistics for the scraper service"}, "response": []}, {"name": "Get Scrape History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/scraper/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "scraper", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get history of scrape jobs"}, "response": []}, {"name": "Cancel Scrape Job", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"jobId\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/scraper/cancel", "host": ["{{baseUrl}}"], "path": ["api", "scraper", "cancel"]}, "description": "Cancel a running scrape job"}, "response": []}, {"name": "Get Scrape Job Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/scraper/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "scraper", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "123456", "description": "ID of the scrape job to get details for"}]}, "description": "Get details of a specific scrape job"}, "response": []}], "description": "Web scraper API endpoints for extracting data from URLs"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8001", "type": "string"}, {"key": "token", "value": "", "type": "string"}]}