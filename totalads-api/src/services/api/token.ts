import { eq, and, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { postgresDb, Service, getApiTokens } from "../../utils/sharedModuleLoader";

// Using async initialization pattern for models
let apiTokens: any = null;

// Initialize apiTokens immediately to avoid delays in service calls
getApiTokens().then(tokens => {
    apiTokens = tokens;
}).catch(err => {
    console.error('Failed to initialize apiTokens model:', err);
});


export const GenerateTokenDataSchema = z.object({
    userId: z.bigint(),
    name: z.string().min(1).max(100),
});

export const RevokeTokenDataSchema = z.object({
    userId: z.bigint(),
    tokenId: z.bigint(),
});

export const GetTokensDataSchema = z.object({
    userId: z.bigint(),
});

export interface GenerateTokenServiceData {
    userId: bigint;
    name: string;
}

export interface RevokeTokenServiceData {
    userId: bigint;
    tokenId: bigint;
}

export interface GetTokensServiceData {
    userId: bigint;
}

export interface ApiToken {
    id: string;
    name: string;
    token: string;
    createdAt: Date;
    lastUsedAt: Date | null;
    active: boolean;
}

export class GenerateTokenService extends Service<GenerateTokenServiceData, ApiToken> {
    async handle(): Promise<ApiToken> {
        // Generate a secure random API token with a prefix for easy identification
        const token = `ta_${uuidv4().replace(/-/g, '')}`;
        
        const result = await postgresDb.transaction(async (db) => {
            const [newToken] = await db.insert(apiTokens)
                .values({
                    userId: this.data.userId,
                    token: token,
                    name: this.data.name,
                    active: true,
                })
                .returning({
                    id: apiTokens.id,
                    name: apiTokens.name,
                    token: apiTokens.token,
                    createdAt: apiTokens.createdAt,
                    lastUsedAt: apiTokens.lastUsedAt,
                    active: apiTokens.active,
                });
            
            return newToken;
        });

        if (!result) {
            throw new Error('Failed to create token');
        }
        
        return {
            id: result.id.toString(),
            name: result.name,
            token: result.token,
            createdAt: result.createdAt!,
            lastUsedAt: result.lastUsedAt || null,
            active: result.active,
        };
    }
}

export class RevokeTokenService extends Service<RevokeTokenServiceData, boolean> {
    async handle(): Promise<boolean> {
        const result = await postgresDb.transaction(async (db) => {
            const now = new Date();
            
            const [updatedToken] = await db.update(apiTokens)
                .set({ 
                    active: false,
                    revokedAt: now,
                })
                .where(
                    and(
                        eq(apiTokens.id, this.data.tokenId),
                        eq(apiTokens.userId, this.data.userId)
                    )
                )
                .returning({ id: apiTokens.id });
            
            return updatedToken !== undefined;
        });

        return result;
    }
}

export class GetTokensService extends Service<GetTokensServiceData, ApiToken[]> {
    async handle(): Promise<ApiToken[]> {
        const tokens = await postgresDb.transaction(async (db) => {
            const results = await db.select({
                id: apiTokens.id,
                name: apiTokens.name,
                token: apiTokens.token,
                createdAt: apiTokens.createdAt,
                lastUsedAt: apiTokens.lastUsedAt,
                active: apiTokens.active,
            })
            .from(apiTokens)
            .where(eq(apiTokens.userId, this.data.userId));
            
            return results;
        });

        return tokens.map(token => ({
            id: token.id.toString(),
            name: token.name,
            // Mask token except for the last 8 characters for security
            token: token.token.replace(/^(.*)(.{8})$/, '••••••••$2'),
            createdAt: token.createdAt!,
            lastUsedAt: token.lastUsedAt || null,
            active: token.active,
        }));
    }
}

export default {
    GenerateTokenService,
    RevokeTokenService,
    GetTokensService,
    GenerateTokenDataSchema,
    RevokeTokenDataSchema,
    GetTokensDataSchema,
};
