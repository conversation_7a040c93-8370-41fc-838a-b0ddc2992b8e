import axios, { AxiosRequestConfig } from 'axios';
import { z } from 'zod';
import { Service } from 'totalads-shared';
import { TrackApiUsageService } from './usage';

// Environment variables
const SCRAPER_SERVICE_URL = process.env.SCRAPER_SERVICE_URL || 'http://localhost:3001';

// Schema for scrape URL validation
export const ScrapeUrlDataSchema = z.object({
    url: z.string().url(),
    enableAI: z.boolean().optional().default(false),
});

export interface ScrapeUrlServiceData {
    url: string;
    enableAI?: boolean;
    // These are set by the middleware
    userId: bigint;
    tokenId: bigint;
    requestId: string;
}

export class ScrapeUrlService extends Service<ScrapeUrlServiceData, any> {
    async handle(): Promise<any> {
        try {
            // Check if this request should be charged (over free tier limit)
            const shouldCharge = await TrackApiUsageService.shouldCharge(this.data.userId);
            
            // Forward the request to the scraper service
            const response = await axios({
                method: 'POST',
                url: `${SCRAPER_SERVICE_URL}/scrape`,
                data: {
                    url: this.data.url,
                    enableAI: this.data.enableAI || false,
                },
                headers: {
                    'Content-Type': 'application/json',
                    'X-Request-Id': this.data.requestId,
                },
                validateStatus: () => true, // Don't throw on non-2xx responses
            });
            
            // Track the API usage for statistics and billing
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape',
                requestId: this.data.requestId,
                responseStatus: response.status,
            });
            
            // Add billing metadata to the response
            const responseWithBilling = {
                ...response.data,
                meta: {
                    ...response.data.meta,
                    billing: {
                        charged: shouldCharge,
                        rate: shouldCharge ? 0.05 : 0, // $0.05 per call if charged
                    },
                },
            };
            
            return responseWithBilling;
        } catch (error) {
            console.error('Error proxying to scraper service:', error);
            
            // Track failed request
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape',
                requestId: this.data.requestId,
                responseStatus: 500,
            });
            
            throw new Error('Failed to proxy request to scraper service');
        }
    }
}

export class GetScraperHealthService extends Service<{ requestId: string; userId: bigint; tokenId: bigint }, any> {
    async handle(): Promise<any> {
        try {
            const response = await axios.get(`${SCRAPER_SERVICE_URL}/scrape/health`);
            
            // Track the API health check (but don't charge for it)
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape/health',
                requestId: this.data.requestId,
                responseStatus: response.status,
            });
            
            return response.data;
        } catch (error) {
            console.error('Error checking scraper health:', error);
            throw new Error('Failed to check scraper health');
        }
    }
}

// Get usage statistics from the scraper service
export class GetScraperUsageService extends Service<{ requestId: string; userId: bigint; tokenId: bigint }, any> {
    async handle(): Promise<any> {
        try {
            const response = await axios.get(`${SCRAPER_SERVICE_URL}/scrape/usage`);
            
            // Track the API usage request (but don't charge for it)
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape/usage',
                requestId: this.data.requestId,
                responseStatus: response.status,
            });
            
            return response.data;
        } catch (error) {
            console.error('Error getting scraper usage:', error);
            throw new Error('Failed to get scraper usage statistics');
        }
    }
}

// Get history of scrape jobs
export interface GetScrapeHistoryServiceData {
    userId: bigint;
    tokenId: bigint;
    requestId?: string;
    page?: number;
    limit?: number;
}

export class GetScrapeHistoryService extends Service<GetScrapeHistoryServiceData, any> {
    async handle(): Promise<any> {
        try {
            const { page = 1, limit = 10 } = this.data;
            const response = await axios.get(`${SCRAPER_SERVICE_URL}/scrape/history`, {
                params: { page, limit },
                headers: {
                    'X-User-ID': this.data.userId.toString(),
                }
            });
            
            // Track the API history request (but don't charge for it)
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape/history',
                requestId: this.data.requestId || 'no-request-id',
                responseStatus: response.status,
            });
            
            return response.data;
        } catch (error) {
            console.error('Error getting scrape history:', error);
            throw new Error('Failed to get scrape history');
        }
    }
}

// Cancel a scrape job
export interface CancelScrapeJobServiceData {
    userId: bigint;
    tokenId: bigint;
    requestId?: string;
    jobId: string;
}

export class CancelScrapeJobService extends Service<CancelScrapeJobServiceData, any> {
    async handle(): Promise<any> {
        try {
            const response = await axios.post(`${SCRAPER_SERVICE_URL}/scrape/cancel`, {
                jobId: this.data.jobId,
                userId: this.data.userId.toString(),
            });
            
            // Track the API cancel request (but don't charge for it)
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: '/scrape/cancel',
                requestId: this.data.requestId || 'no-request-id',
                responseStatus: response.status,
            });
            
            return response.data;
        } catch (error) {
            console.error('Error canceling scrape job:', error);
            throw new Error(`Failed to cancel scrape job: ${this.data.jobId}`);
        }
    }
}

// Get details of a specific scrape job
export interface GetScrapeJobDetailsServiceData {
    userId: bigint;
    tokenId: bigint;
    requestId?: string;
    jobId: string;
}

export class GetScrapeJobDetailsService extends Service<GetScrapeJobDetailsServiceData, any> {
    async handle(): Promise<any> {
        try {
            const response = await axios.get(`${SCRAPER_SERVICE_URL}/scrape/jobs/${this.data.jobId}`, {
                headers: {
                    'X-User-ID': this.data.userId.toString(),
                }
            });
            
            // Track the API job details request (but don't charge for it)
            await TrackApiUsageService.track({
                userId: this.data.userId,
                tokenId: this.data.tokenId,
                endpoint: `/scrape/jobs/${this.data.jobId}`,
                requestId: this.data.requestId || 'no-request-id',
                responseStatus: response.status,
            });
            
            return response.data;
        } catch (error) {
            console.error('Error getting scrape job details:', error);
            throw new Error(`Failed to get details for scrape job: ${this.data.jobId}`);
        }
    }
}
