import { eq, and, sql, desc, count } from "drizzle-orm";
import { z } from "zod";
import { postgresDb, Service, getApiUsage, getApiTokens } from "../../utils/sharedModuleLoader";

// Using async initialization pattern for models
let apiUsage: any = null;
let apiTokens: any = null;

// Initialize models immediately to avoid delays in service calls
Promise.all([
    getApiUsage().then(usage => { apiUsage = usage; }),
    getApiTokens().then(tokens => { apiTokens = tokens; })
]).catch(err => {
    console.error('Failed to initialize models:', err);
});

export const GetUsageDataSchema = z.object({
    userId: z.bigint(),
    month: z.string().optional(), // Format: YYYY-MM, defaults to current month
});

export const GetBillingDataSchema = z.object({
    userId: z.bigint(),
    month: z.string().optional(), // Format: YYYY-MM, defaults to current month
});

export interface GetUsageServiceData {
    userId: bigint;
    month?: string;
}

export interface GetBillingServiceData {
    userId: bigint;
    month?: string;
}

export interface UsageStats {
    total: number;
    free: number;
    billable: number;
    byEndpoint: Record<string, number>;
    byToken: Record<string, number>;
}

export interface BillingSummary {
    totalCalls: number;
    freeCalls: number;
    billableCalls: number;
    rate: number; // In dollars
    totalAmount: number; // In dollars
    month: string;
}

// Configurable settings
const FREE_TIER_CALLS = 10; // Free calls per month
const RATE_PER_CALL = 0.05; // $0.05 per API call

export class TrackApiUsageService {
    /**
     * Track API usage for billing and statistics
     */
    static async track(data: {
        userId: bigint;
        tokenId: bigint;
        endpoint: string;
        requestId: string;
        responseStatus: number;
    }): Promise<void> {
        const currentMonth = new Date().toISOString().substring(0, 7); // Format: YYYY-MM
        
        await postgresDb.transaction(async (db) => {
            // Record the API call
            await db.insert(apiUsage).values({
                userId: data.userId,
                tokenId: data.tokenId,
                endpoint: data.endpoint,
                requestId: data.requestId,
                responseStatus: data.responseStatus,
                month: currentMonth,
            });
            
            // Update the last used timestamp for the token
            await db.update(apiTokens)
                .set({ lastUsedAt: new Date() })
                .where(eq(apiTokens.id, data.tokenId));
        });
    }

    /**
     * Determine if a call should be charged based on usage limits
     */
    static async shouldCharge(userId: bigint): Promise<boolean> {
        const currentMonth = new Date().toISOString().substring(0, 7); // Format: YYYY-MM
        
        const result = await postgresDb.select({
            count: count()
        })
        .from(apiUsage)
        .where(
            and(
                eq(apiUsage.userId, userId),
                eq(apiUsage.month, currentMonth)
            )
        );
        
        const currentCount = result[0]?.count || 0;
        
        // Free tier: first FREE_TIER_CALLS per month
        return currentCount >= FREE_TIER_CALLS;
    }
}

export class GetUsageService extends Service<GetUsageServiceData, UsageStats> {
    async handle(): Promise<UsageStats> {
        // Default to current month if not specified
        const month = this.data.month || new Date().toISOString().substring(0, 7);
        
        const result = await postgresDb.transaction(async (db) => {
            // Get total calls
            const totalCountResult = await db.select({
                count: count()
            })
            .from(apiUsage)
            .where(
                and(
                    eq(apiUsage.userId, this.data.userId),
                    eq(apiUsage.month, month)
                )
            );
            
            const totalCount = totalCountResult[0]?.count || 0;
            
            // If no usage, return early
            if (totalCount === 0) {
                return {
                    total: 0,
                    free: 0,
                    billable: 0,
                    byEndpoint: {},
                    byToken: {}
                };
            }
            
            // Calculate free vs billable
            const free = Math.min(totalCount, FREE_TIER_CALLS);
            const billable = Math.max(0, totalCount - FREE_TIER_CALLS);
            
            // Get usage by endpoint
            const byEndpointResult = await db.select({
                endpoint: apiUsage.endpoint,
                count: count()
            })
            .from(apiUsage)
            .where(
                and(
                    eq(apiUsage.userId, this.data.userId),
                    eq(apiUsage.month, month)
                )
            )
            .groupBy(apiUsage.endpoint);
            
            // Get usage by token
            const byTokenResult = await db.select({
                tokenId: apiUsage.tokenId,
                tokenName: apiTokens.name,
                count: count()
            })
            .from(apiUsage)
            .leftJoin(apiTokens, eq(apiUsage.tokenId, apiTokens.id))
            .where(
                and(
                    eq(apiUsage.userId, this.data.userId),
                    eq(apiUsage.month, month)
                )
            )
            .groupBy(apiUsage.tokenId, apiTokens.name);
            
            // Format the results
            const byEndpoint: Record<string, number> = {};
            byEndpointResult.forEach(item => {
                byEndpoint[item.endpoint] = Number(item.count);
            });
            
            const byToken: Record<string, number> = {};
            byTokenResult.forEach(item => {
                const tokenName = item.tokenName || `Token ${item.tokenId?.toString()}`;
                byToken[tokenName] = Number(item.count);
            });
            
            return {
                total: totalCount,
                free,
                billable,
                byEndpoint,
                byToken
            };
        });
        
        return result;
    }
}

export class GetBillingService extends Service<GetBillingServiceData, BillingSummary> {
    async handle(): Promise<BillingSummary> {
        // Default to current month if not specified
        const month = this.data.month || new Date().toISOString().substring(0, 7);
        
        const usageService = new GetUsageService({ 
            userId: this.data.userId,
            month 
        });
        
        const usage = await usageService.handle();
        
        return {
            totalCalls: usage.total,
            freeCalls: usage.free,
            billableCalls: usage.billable,
            rate: RATE_PER_CALL,
            totalAmount: parseFloat((usage.billable * RATE_PER_CALL).toFixed(2)),
            month
        };
    }
}

export default {
    GetUsageService,
    GetBillingService,
    TrackApiUsageService,
    GetUsageDataSchema,
    GetBillingDataSchema,
    FREE_TIER_CALLS,
    RATE_PER_CALL
};
