import '../config/loadEnv';

import { Server } from 'totalads-shared';
import express, { Router } from 'express';
import path from 'path';

import authRouter from './routes/auth';
import usersRouter from './routes/users';
import apiManagementRouter from './routes/apiManagement';
import scraperApiRouter from './routes/scraperApi';
import healthRouter from './routes/health';

// Create router for static files
const staticRouter = Router();
staticRouter.use(express.static(path.join(process.cwd(), 'public')));

new Server({
	name: "totalads-api",
	port: +(process.env.PORT as string),
	routes: [
		{
			// Root path for static files and standalone swagger UI
			path: "/",
			handlers: [staticRouter],
		},
		{
			path: "/health",
			handlers: [healthRouter],
		},
		{
			path: "/auth",
			handlers: [authRouter],
		},
		{
			path: "/users",
			handlers: [usersRouter],
		},
		{
			path: "/api-management",
			handlers: [apiManagementRouter],
		},
		{
			path: "/api/scraper",
			handlers: [scraperApiRouter],
		}
	],
});
