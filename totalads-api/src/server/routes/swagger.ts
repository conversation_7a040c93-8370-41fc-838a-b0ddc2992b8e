import { Router, Request, Response } from 'express';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from '../../config/swagger';
import fs from 'fs';
import path from 'path';

const swaggerRouter = Router();

// Configure SwaggerUI options
const swaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    persistAuthorization: true,
  },
  customCssUrl: '/swagger-ui-custom.css', // Optional custom styling
  customSiteTitle: 'TotalAds API Documentation',
};

// Serve Swagger UI assets
swaggerRouter.use('/', swaggerUi.serve);

// Serve Swagger UI
swaggerRouter.get('/', (req: Request, res: Response, next: Function) => {
  try {
    // Add proper headers to ensure browser renders HTML correctly
    res.setHeader('Content-Type', 'text/html');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // Use the setup function with options
    const html = swaggerUi.generateHTML(swaggerSpec, swaggerUiOptions);
    return res.send(html);
  } catch (error) {
    console.error('Error serving Swagger UI:', error);
    // Send a user-friendly error instead of passing to the error handler
    res.status(500).send('<h1>Error loading Swagger UI</h1><pre>' + 
      (error instanceof Error ? error.message : String(error)) + '</pre>');
  }
});

// Expose swagger.json endpoint
swaggerRouter.get('/json', (req: Request, res: Response) => {
  try {
    res.setHeader('Content-Type', 'application/json');
    // Explicitly stringify to ensure proper JSON format
    const safeSpec = JSON.parse(JSON.stringify(swaggerSpec));
    res.send(JSON.stringify(safeSpec));
  } catch (error) {
    console.error('Error serving swagger.json:', error);
    res.status(500).json({
      error: 'Failed to generate Swagger documentation',
      message: error instanceof Error ? error.message : String(error)
    });
  }
});

export default swaggerRouter;
