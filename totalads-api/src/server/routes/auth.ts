import { Router, Request, Response } from 'express';
import {
    authMiddlewareCreator, cookieSetterFromUserId, expressAsyncHandler
} from 'totalads-shared';

import LoginService, { LoginDataSchema } from '../../services/auth/login';
import ResetPasswordService, { ResetPasswordDataSchema } from '../../services/auth/resetPassword';
import SendEmailVerificationCodeService from '../../services/auth/sendEmailVerificationCode';
import SendResetPasswordCodeService, {
    SendResetPasswordCodeDataSchema
} from '../../services/auth/sendResetPasswordCode';
import SignupService, { SignupDataSchema } from '../../services/auth/signup';
import ValidateResetPasswordCodeService, {
    ValidateResetPasswordCodeDataSchema
} from '../../services/auth/validateResetPasswordCode';
import VerifyEmailService, {
    VerifyEmailWithoutAuthDataSchema
} from '../../services/auth/verifyEmail';

const cookieExpiryInSeconds = +(process.env.COOKIE_EXPIRY_IN_SECONDS as string);

const authRouter = Router();

authRouter.post(
	"/login",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			const loginService = new LoginService(validatedData);
			const userId = await loginService.execute();
			await cookieSetterFromUserId({
				userId,
				cookieExpiryInSeconds,
				res,
			});
			return res.status(200).send("Logged in successfully!");
		},
		{
			validationSchema: LoginDataSchema,
			getValue: (req) => req.body,
		}
	),
);

authRouter.post(
	"/signup",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			const signupService = new SignupService(validatedData);
			const userId = await signupService.execute();
			await cookieSetterFromUserId({
				userId,
				cookieExpiryInSeconds,
				res,
			});
			return res.status(200).send("Signed up successfully!");
		},
		{
			validationSchema: SignupDataSchema,
			getValue: (req) => req.body,
		},
	),
);

authRouter.post(
	"/reset-password/send-code",
	expressAsyncHandler(
		async (validatedData, req: Request, res) => {
			const sendResetPasswordCodeService =
				new SendResetPasswordCodeService(validatedData);
			await sendResetPasswordCodeService.execute();
			return res
				.status(200)
				.send("Reset password email sent successfully!");
		},
		{
			validationSchema: SendResetPasswordCodeDataSchema,
			getValue: (req) => req.body,
		},
	),
);

authRouter.get(
	"/reset-password/validate-code",
	expressAsyncHandler(
		async (validatedData, _, res) => {
			const validateResetPasswordCodeService =
				new ValidateResetPasswordCodeService(validatedData);
			const result = await validateResetPasswordCodeService.execute();
			return res.status(200).send(result);
		},
		{
			validationSchema: ValidateResetPasswordCodeDataSchema,
			getValue: (req) => req.query,
		},
	),
);

authRouter.patch(
	"/reset-password/reset",
	expressAsyncHandler(
		async (validatedData, req: Request, res) => {
			const resetPasswordService = new ResetPasswordService(
				validatedData,
			);
			await resetPasswordService.execute();
			return res.status(200).send("Password resetted successfully!");
		},
		{
			validationSchema: ResetPasswordDataSchema,
			getValue: (req) => req.body,
		},
	),
);

authRouter.patch(
	"/email-verification/verify",
	authMiddlewareCreator(),
	expressAsyncHandler(
		async (validatedData, req: Request, res: Response) => {
			// Check if userId exists in the request
			if (!req.userId) {
				return res.status(401).send("User not authenticated");
			}
			
			const verifyEmailService = new VerifyEmailService({
				userId: req.userId,
				...validatedData,
			});
			await verifyEmailService.execute();
			return res.status(200).send("Email verified successfully!");
		},
		{
			validationSchema: VerifyEmailWithoutAuthDataSchema,
			getValue: (req) => req.body,
		},
	),
);

authRouter.post(
	"/email-verification/send-code",
	authMiddlewareCreator(),
	expressAsyncHandler(async (req, res) => {
		const sendEmailVerificationCodeService =
			new SendEmailVerificationCodeService({
				userId: req.userId as bigint,
			});
		await sendEmailVerificationCodeService.execute();
		return res.status(200).send("Verification email sent successfully!");
	}),
);

authRouter.delete("/logout", authMiddlewareCreator(), (req: Request, res: Response) => {
	res.clearCookie("auth", {
		domain: "secondbrainlabs.com",
		path: "/",
		sameSite: "none",
		httpOnly: true,
		secure: true,
	});
	return res.status(200).send("Logged out successfully!");
});

export default authRouter;
