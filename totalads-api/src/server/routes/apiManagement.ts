import { Router, Request, Response } from 'express';
import { authMiddlewareCreator, expressAsyncHandler } from 'totalads-shared';

import { 
    GenerateTokenService, 
    RevokeTokenService, 
    GetTokensService,
    GenerateTokenDataSchema,
    RevokeTokenDataSchema 
} from '../../services/api/token';
import { 
    GetUsageService, 
    GetBillingService,
    GetUsageDataSchema,
    GetBillingDataSchema
} from '../../services/api/usage';

const apiManagementRouter = Router();

// All routes require authentication
apiManagementRouter.use(authMiddlewareCreator());

/**
 * @swagger
 * /api-management/tokens:
 *   post:
 *     summary: Generate a new API token
 *     tags: [API Management]
 *     security:
 *       - cookieAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name for the API token
 *     responses:
 *       201:
 *         description: API token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/ApiToken'
 *                 message:
 *                   type: string
 *                   example: API token generated successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
apiManagementRouter.post(
    "/tokens",
    expressAsyncHandler(
        async (validatedData, req, res) => {
            const generateTokenService = new GenerateTokenService({
                userId: req.userId as bigint,
                name: validatedData.name,
            });

            const token = await generateTokenService.handle();
            return res.status(201).json({
                success: true,
                data: token,
                message: "API token generated successfully"
            });
        },
        {
            validationSchema: GenerateTokenDataSchema,
            getValue: (req) => req.body
        }
    )
);

/**
 * @swagger
 * /api-management/tokens:
 *   get:
 *     summary: Get all API tokens for the authenticated user
 *     tags: [API Management]
 *     security:
 *       - cookieAuth: []
 *     responses:
 *       200:
 *         description: List of API tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ApiToken'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
apiManagementRouter.get(
    "/tokens",
    expressAsyncHandler(async (req, res) => {
        const getTokensService = new GetTokensService({
            userId: req.userId as bigint
        });

        const tokens = await getTokensService.handle();
        return res.status(200).json({
            success: true,
            data: tokens
        });
    })
);

/**
 * @swagger
 * /api-management/tokens/{tokenId}:
 *   delete:
 *     summary: Revoke an API token
 *     tags: [API Management]
 *     security:
 *       - cookieAuth: []
 *     parameters:
 *       - in: path
 *         name: tokenId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the API token to revoke
 *     responses:
 *       200:
 *         description: API token revoked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: API token revoked successfully
 *       404:
 *         description: API token not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: API token not found or already revoked
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
apiManagementRouter.delete(
    "/tokens/:tokenId",
    expressAsyncHandler(
        async (validatedData, req, res) => {
            const revokeTokenService = new RevokeTokenService({
                userId: req.userId as bigint,
                tokenId: BigInt(validatedData.tokenId)
            });

            const result = await revokeTokenService.handle();
            
            if (result) {
                return res.status(200).json({
                    success: true,
                    message: "API token revoked successfully"
                });
            } else {
                return res.status(404).json({
                    success: false,
                    message: "API token not found or already revoked"
                });
            }
        },
        {
            validationSchema: RevokeTokenDataSchema,
            getValue: (req) => ({ 
                tokenId: req.params.tokenId 
            })
        }
    )
);

/**
 * @swagger
 * /api-management/usage:
 *   get:
 *     summary: Get API usage statistics
 *     tags: [API Management]
 *     security:
 *       - cookieAuth: []
 *     parameters:
 *       - in: query
 *         name: month
 *         schema:
 *           type: string
 *           format: YYYY-MM
 *           example: '2025-07'
 *         description: Month to get usage statistics for (defaults to current month)
 *     responses:
 *       200:
 *         description: API usage statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/UsageStats'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
apiManagementRouter.get(
    "/usage",
    expressAsyncHandler(
        async (validatedData, req, res) => {
            const getUsageService = new GetUsageService({
                userId: req.userId as bigint,
                month: validatedData.month
            });

            const usage = await getUsageService.handle();
            return res.status(200).json({
                success: true,
                data: usage
            });
        },
        {
            validationSchema: GetUsageDataSchema,
            getValue: (req) => ({ 
                month: req.query.month as string | undefined 
            })
        }
    )
);

/**
 * @swagger
 * /api-management/billing:
 *   get:
 *     summary: Get billing information
 *     tags: [API Management]
 *     security:
 *       - cookieAuth: []
 *     parameters:
 *       - in: query
 *         name: month
 *         schema:
 *           type: string
 *           format: YYYY-MM
 *           example: '2025-07'
 *         description: Month to get billing information for (defaults to current month)
 *     responses:
 *       200:
 *         description: Billing information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/BillingInfo'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
apiManagementRouter.get(
    "/billing",
    expressAsyncHandler(
        async (validatedData, req, res) => {
            const getBillingService = new GetBillingService({
                userId: req.userId as bigint,
                month: validatedData.month
            });

            const billing = await getBillingService.handle();
            return res.status(200).json({
                success: true,
                data: billing
            });
        },
        {
            validationSchema: GetBillingDataSchema,
            getValue: (req) => ({ 
                month: req.query.month as string | undefined 
            })
        }
    )
);

export default apiManagementRouter;
