import { Router, Request, Response, NextFunction } from 'express';
import { expressAs<PERSON>Hand<PERSON> } from 'totalads-shared';

import apiTokenAuthMiddleware from '../../middleware/apiTokenAuth';
import { 
    ScrapeUrlService, 
    GetScraperHealthService,
    GetScraperUsageService,
    GetScrapeHistoryService,
    CancelScrapeJobService,
    GetScrapeJobDetailsService,
    ScrapeUrlDataSchema 
} from '../../services/api/scraperProxy';

const scraperApiRouter = Router();

// All routes require API token authentication
scraperApiRouter.use(apiTokenAuthMiddleware);

/**
 * @swagger
 * /api/scraper:
 *   post:
 *     summary: Scrape a URL
 *     description: Proxy endpoint for the scraper service to extract data from a URL
 *     tags: [Scraper API]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - url
 *             properties:
 *               url:
 *                 type: string
 *                 format: uri
 *                 description: URL to scrape
 *                 example: https://example.com
 *               enableAI:
 *                 type: boolean
 *                 default: false
 *                 description: Enable AI processing of scraped content
 *     responses:
 *       200:
 *         description: Successful scrape operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 meta:
 *                   type: object
 *                   properties:
 *                     billing:
 *                       type: object
 *                       properties:
 *                         charged:
 *                           type: boolean
 *                         apiCall:
 *                           type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       429:
 *         description: API rate limit exceeded
 *       500:
 *         description: Server error during scraping operation
 */
scraperApiRouter.post(
    "/",
    expressAsyncHandler((
        async (validatedData: any, req: any, res: Response, next?: NextFunction) => {
            // Using any to bypass type compatibility issues between different Express versions
            const scrapeUrlService = new ScrapeUrlService({
                url: validatedData.url,
                enableAI: validatedData.enableAI,
                userId: req.userId as bigint,
                tokenId: (req as any).tokenId,
                requestId: (req as any).requestId
            });

            const result = await scrapeUrlService.handle();
            return res.status(200).json(result);
        }) as any,
        {
            validationSchema: ScrapeUrlDataSchema,
            getValue: (req) => req.body
        }
    )
);

/**
 * @swagger
 * /api/scraper/health:
 *   get:
 *     summary: Check scraper service health
 *     description: Verify that the scraper service is running and accessible
 *     tags: [Scraper API]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Scraper service health check result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         description: Failed to check scraper health
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: Failed to check scraper health
 */
scraperApiRouter.get("/health", async (req: Request, res: Response) => {
    try {
        const getScraperHealthService = new GetScraperHealthService({
            userId: req.userId as bigint,
            tokenId: (req as any).tokenId,
            requestId: (req as any).requestId
        });

        const result = await getScraperHealthService.handle();
        return res.status(200).json(result);
    } catch (error) {
        console.error('Error checking scraper health:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to check scraper health'
        });
    }
});

// For getting usage statistics
scraperApiRouter.get("/usage", async (req: Request, res: Response) => {
    try {
        const getScraperUsageService = new GetScraperUsageService({
            userId: req.userId as bigint,
            tokenId: (req as any).tokenId,
            requestId: (req as any).requestId
        });

        const result = await getScraperUsageService.handle();
        return res.status(200).json(result);
    } catch (error) {
        console.error('Error getting scraper usage:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to get scraper usage'
        });
    }
});

// For getting scrape history
scraperApiRouter.get("/history", async (req: Request, res: Response) => {
    try {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        
        const getScrapeHistoryService = new GetScrapeHistoryService({
            userId: req.userId as bigint,
            tokenId: (req as any).tokenId,
            page,
            limit
        });

        const result = await getScrapeHistoryService.handle();
        return res.status(200).json(result);
    } catch (error) {
        console.error('Error getting scrape history:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to get scrape history'
        });
    }
});

// For canceling jobs
scraperApiRouter.post("/cancel", async (req: Request, res: Response) => {
    try {
        const { jobId } = req.body;
        
        const cancelScrapeJobService = new CancelScrapeJobService({
            userId: req.userId as bigint,
            tokenId: (req as any).tokenId,
            jobId
        });

        const result = await cancelScrapeJobService.handle();
        return res.status(200).json(result);
    } catch (error) {
        console.error('Error canceling scrape job:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to cancel scrape job'
        });
    }
});

// For getting job details
scraperApiRouter.get("/jobs/:jobId", async (req: Request, res: Response) => {
    try {
        const { jobId } = req.params;
        
        const getScrapeJobDetailsService = new GetScrapeJobDetailsService({
            userId: req.userId as bigint,
            tokenId: (req as any).tokenId,
            jobId
        });

        const result = await getScrapeJobDetailsService.handle();
        return res.status(200).json(result);
    } catch (error) {
        console.error('Error getting job details:', error);
        return res.status(500).json({
            success: false,
            error: 'Failed to get job details'
        });
    }
});

export default scraperApiRouter;
