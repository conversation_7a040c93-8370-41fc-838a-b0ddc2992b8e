/**
 * Utility to handle module loading from totalads-shared in both ESM and CommonJS environments.
 * This abstracts the complexity of importing from totalads-shared while working around the exports limitations.
 */

// Import the base shared module exports that work in both ESM and CJS environments
import { postgresDb, Service } from 'totalads-shared';

// Get the runtime module system
const isEsm = typeof require === 'undefined';

// Define the model structure
interface ModelsType {
  apiTokens?: any;
  apiUsage?: any;
  users?: any;
  [key: string]: any;
}

/**
 * Load models from the main totalads-shared export
 * Now that we've added proper exports to totalads-shared/src/index.ts, 
 * this should be the primary method to load models.
 */
const loadModelsFromMainExport = async (): Promise<ModelsType> => {
  try {
    if (isEsm) {
      // In ESM mode - dynamic import
      const { apiTokens, apiUsage, users } = await import('totalads-shared');
      
      if (!apiTokens || !apiUsage || !users) {
        console.warn('Some models missing from totalads-shared main export');
        // Log available exports to help debug
        const mainModule = await import('totalads-shared');
        console.log('Available exports from totalads-shared:', Object.keys(mainModule));
      }
      
      return { 
        apiTokens, 
        apiUsage, 
        users 
      };
    } else {
      // In CommonJS mode - require
      // @ts-ignore - for TypeScript in ESM mode
      const shared = require('totalads-shared');
      const { apiTokens, apiUsage, users } = shared;
      
      if (!apiTokens || !apiUsage || !users) {
        console.warn('Some models missing from totalads-shared main export (CJS)');
        console.log('Available exports from totalads-shared (CJS):', Object.keys(shared));
      }
      
      return { 
        apiTokens, 
        apiUsage, 
        users 
      };
    }
  } catch (error: any) {
    console.error('Error loading models from main export:', error);
    throw new Error(`Failed to load models from main export: ${error.message}`);
  }
};

/**
 * Create mock models as fallback in case the real models are unavailable
 * This should only be used in development or emergency situations
 */
const createMockModels = (): ModelsType => {
  console.warn('Creating mock models as fallbacks - NOT FOR PRODUCTION USE');
  
  // These are incomplete mock models that should be replaced with real implementations
  const apiTokens = {
    tableName: 'api_tokens',
    schema: {
      token: 'string',
      userId: 'bigint',
      createdAt: 'timestamp',
      updatedAt: 'timestamp'
    }
  };
  
  const apiUsage = {
    tableName: 'api_usage',
    schema: {
      id: 'bigint',
      userId: 'bigint',
      tokenId: 'string',
      timestamp: 'timestamp',
      endpoint: 'string',
      requestCount: 'integer',
      responseTime: 'integer',
      status: 'string'
    }
  };
  
  const users = {
    tableName: 'users',
    schema: {
      id: 'bigint',
      email: 'string',
      password: 'string',
      createdAt: 'timestamp',
      updatedAt: 'timestamp'
    }
  };
  
  return { apiTokens, apiUsage, users };
};

/**
 * Load models using the best available method
 * Prioritizes the main export (which should now contain all required models)
 * and only falls back to mocks if absolutely necessary
 */
export const loadModels = async (): Promise<ModelsType> => {
  try {
    // First try to load models directly from the main export
    // This should now work with our updated totalads-shared package
    console.log('Loading models from totalads-shared main export...');
    const models = await loadModelsFromMainExport();
    
    // Check if we got all the models we need
    const missingModels = [];
    if (!models.apiTokens) missingModels.push('apiTokens');
    if (!models.apiUsage) missingModels.push('apiUsage');
    if (!models.users) missingModels.push('users');
    
    // If all models were successfully loaded, return them
    if (missingModels.length === 0) {
      console.log('All models loaded successfully from main export');
      return models;
    }
    
    // Some models are missing, we need to use mocks as fallback
    console.warn(`Missing models from main export: ${missingModels.join(', ')}. Using mock models as fallback.`);
    const mockModels = createMockModels();
    
    // Return a combination of real and mock models
    return {
      apiTokens: models.apiTokens || mockModels.apiTokens,
      apiUsage: models.apiUsage || mockModels.apiUsage,
      users: models.users || mockModels.users,
    };
  } catch (error: any) {
    console.error('Failed to load models from main export, using mock models:', error);
    
    // As a last resort, return mock models
    const mockModels = createMockModels();
    return mockModels;
  }
};

// Common exports that work in both ESM and CJS
export { postgresDb, Service };

// Direct model exports for convenience - preload common models
let _apiTokens: any = null;
let _apiUsage: any = null;
let _users: any = null;
let _modelsLoading: Promise<any> | null = null;

// Create a single loading promise to avoid multiple parallel loads
const ensureModelsLoaded = async () => {
  if (!_modelsLoading) {
    _modelsLoading = loadModels().then(models => {
      _apiTokens = models.apiTokens;
      _apiUsage = models.apiUsage;
      _users = models.users;
      return models;
    }).catch(err => {
      console.error('Failed to preload shared models:', err);
      _modelsLoading = null; // Reset so we can try again
      throw err;
    });
  }
  return _modelsLoading;
};

// Start loading models immediately
ensureModelsLoaded().catch(() => {});

// Getter functions to access preloaded models
export const getApiTokens = async () => {
  if (!_apiTokens) {
    await ensureModelsLoaded();
  }
  return _apiTokens;
};

export const getApiUsage = async () => {
  if (!_apiUsage) {
    await ensureModelsLoaded();
  }
  return _apiUsage;
};

export const getUsers = async () => {
  if (!_users) {
    await ensureModelsLoaded();
  }
  return _users;
};
