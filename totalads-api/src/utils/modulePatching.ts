/**
 * Module patching utility for development environments
 * This allows us to patch/mock modules that are problematic in development
 */

// We need to use any to interact with <PERSON>de's module system
// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare const require: any;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare const module: any;

import { createMockResend } from './mockResend';

/**
 * Setup module patching for development mode
 * Call this before any other imports that might use the modules we want to patch
 */
export function setupModulePatching(): void {
  // Only apply in development and when the flag is set
  if (process.env.NODE_ENV !== 'production' && process.env.USE_MOCK_EMAIL_CLIENT === 'true') {
    try {
      // Module patching only works in CommonJS mode
      if (typeof require === 'undefined') {
        console.warn('⚠️ Module patching only works in CommonJS mode, not ESM');
        return;
      }
      
      // Get the Module prototype to patch require
      const Module = require('module');
      const originalRequire = Module.prototype.require;
      
      // Patch the require function to intercept specific modules
      Module.prototype.require = function patchedRequire(path: string) {
        // Intercept the Resend module
        if (path === 'resend') {
          console.log('📧 Intercepted require("resend"), returning mock implementation');
          return {
            Resend: function MockResend() {
              return createMockResend();
            }
          };
        }
        
        // Pass through to the original require for all other modules
        return originalRequire.call(this, path);
      };
      
      console.log('✅ Module patching setup complete - mock email client enabled');
    } catch (err) {
      console.error('⚠️ Failed to setup module patching:', err);
    }
  }
}
