/**
 * Development-only email service that mocks the functionality of Resend
 * This allows the server to start without a valid Resend API key in development mode
 */

// Set a dummy Resend API key for shared package to initialize with
if (process.env.NODE_ENV !== 'production' && !process.env.RESEND_API_KEY) {
  process.env.RESEND_API_KEY = 'dummy_key_for_development';
  console.log('📧 Development mode: Using dummy email service');
}

/**
 * Simple development email logger
 * In development mode without a real API key, emails will be logged but not sent
 */
export const logDevEmail = (to: string, subject: string, html?: string, text?: string) => {
  if (process.env.NODE_ENV !== 'production') {
    console.log('\n📧 DEVELOPMENT EMAIL:');
    console.log(`📧 To: ${to}`);
    console.log(`📧 Subject: ${subject}`);
    if (text) {
      console.log(`📧 Text: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
    }
    if (html) {
      console.log(`📧 HTML: ${html.length} characters`);
    }
    console.log('📧 [End of email - not actually sent in development mode]\n');
  }
};
