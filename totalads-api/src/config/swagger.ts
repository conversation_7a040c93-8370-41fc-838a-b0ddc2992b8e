import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'TotalAds API Documentation',
    version: '1.0.0',
    description: 'API documentation for TotalAds API, including the API Monetization system',
    license: {
      name: 'ISC',
    },
    contact: {
      name: 'TotalAds Team',
    },
  },
  servers: [
    {
      url: `http://localhost:${process.env.PORT || 3000}`,
      description: 'Development server',
    },
  ],
  components: {
    securitySchemes: {
      cookieAuth: {
        type: 'apiKey',
        in: 'cookie',
        name: 'authToken'
      },
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Enter your API token in the format: ta_xxxx',
      },
    },
    schemas: {
      ApiToken: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          token: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
          lastUsedAt: { type: 'string', format: 'date-time', nullable: true },
          active: { type: 'boolean' },
        },
      },
      UsageStats: {
        type: 'object',
        properties: {
          total: { type: 'integer' },
          free: { type: 'integer' },
          billable: { type: 'integer' },
          byEndpoint: { 
            type: 'object',
            additionalProperties: { type: 'integer' }
          },
          byToken: {
            type: 'object',
            additionalProperties: { type: 'integer' }
          }
        },
      },
      BillingInfo: {
        type: 'object',
        properties: {
          totalCalls: { type: 'integer' },
          freeCalls: { type: 'integer' },
          billableCalls: { type: 'integer' },
          rate: { type: 'number', format: 'float' },
          totalAmount: { type: 'number', format: 'float' },
          month: { type: 'string' }
        },
      },
      Error: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          error: { type: 'string' }
        },
      },
    },
    responses: {
      UnauthorizedError: {
        description: 'Access token is missing or invalid',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
    },
  },
};

const options = {
  swaggerDefinition,
  apis: ['./src/server/routes/*.ts', './src/server/routes/*.js'], // Support both .ts and .js extensions
  failOnErrors: true, // Enable failing on errors to catch issues early
};

const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
