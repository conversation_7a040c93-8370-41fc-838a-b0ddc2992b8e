import { NextFunction, Request, Response } from 'express';
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { postgresDb, getApiTokens } from "../utils/sharedModuleLoader";

// Using async initialization pattern for models
let apiTokens: any = null;

// Initialize apiTokens immediately to avoid delays in middleware calls
getApiTokens().then(tokens => {
    apiTokens = tokens;
}).catch(err => {
    console.error('Failed to initialize apiTokens model:', err);
});

/**
 * Middleware for API token authentication
 * This validates the API token and adds userId and tokenId to the request object
 */
export const apiTokenAuthMiddleware = async (
    req: Request,
    res: Response,
    next: NextFunction
): Promise<void> => {
    try {
        // Get API token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({
                success: false,
                error: 'Missing or invalid API token',
                code: 'INVALID_TOKEN'
            });
            return;
        }

        const token = authHeader.split(' ')[1];
        if (!token || !token.startsWith('ta_')) {
            res.status(401).json({
                success: false,
                error: 'Invalid API token format',
                code: 'INVALID_TOKEN_FORMAT'
            });
            return;
        }

        // Find token in database
        const result = await postgresDb
            .select({
                id: apiTokens.id,
                userId: apiTokens.userId,
                active: apiTokens.active
            })
            .from(apiTokens)
            .where(eq(apiTokens.token, token))
            .limit(1);

        const tokenData = result[0];
        
        // Check if token exists and is active
        if (!tokenData || !tokenData.active) {
            res.status(401).json({
                success: false,
                error: 'Invalid or inactive API token',
                code: 'TOKEN_INVALID_OR_INACTIVE'
            });
            return;
        }

        // Add userId and tokenId to request for use in routes
        req.userId = tokenData.userId;
        // Add tokenId as a bigint
        (req as any).tokenId = tokenData.id;
        // Add a unique request ID for tracking
        (req as any).requestId = uuidv4();

        // Continue to the next middleware or route handler
        next();
    } catch (error) {
        console.error('API token authentication error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error during authentication',
            code: 'AUTH_ERROR'
        });
    }
};

export default apiTokenAuthMiddleware;
