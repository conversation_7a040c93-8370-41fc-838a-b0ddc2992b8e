/* eslint-disable @typescript-eslint/no-explicit-any */
import { ParsedQs } from 'qs';

// This extends the Express Request interface to include properties used in our auth middleware
declare namespace Express {
  export interface Request {
    userId?: bigint;
    metadata?: any;
    body: any;
    query: ParsedQs;
    params: any;
    cookies: any;
  }
  
  export interface Response {
    status(code: number): Response;
    send(body: any): Response;
    json(body: any): Response;
    cookie(name: string, value: any, options?: any): Response;
    clearCookie(name: string, options?: any): Response;
  }
}

// Fix compatibility issues between Express v4 and v5
declare module 'express' {
  export interface Request {
    userId?: bigint;
    metadata?: any;
    body: any;
    query: ParsedQs;
    params: any;
    cookies: any;
  }
  
  export interface Response {
    status(code: number): Response;
    send(body: any): Response;
    json(body: any): Response;
    cookie(name: string, value: any, options?: any): Response;
    clearCookie(name: string, options?: any): Response;
  }
  
  export function Router(): any;
}
