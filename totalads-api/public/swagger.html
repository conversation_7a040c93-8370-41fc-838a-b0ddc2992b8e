 <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="TotalAds API Documentation" />
  <title>TotalAds API Documentation</title>
  <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui.css" />
  <style>
    html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
    *, *:before, *:after { box-sizing: inherit; }
    body { margin: 0; background: #fafafa; }
    .error-container { padding: 20px; max-width: 800px; margin: 0 auto; color: #e53935; }
    .swagger-ui .info { margin: 15px 0; }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <div id="error-container" class="error-container" style="display: none;"></div>
  
  <script src="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.18.3/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const errorContainer = document.getElementById('error-container');
      
      try {
        window.ui = SwaggerUIBundle({
          url: "/api-docs/json",
          dom_id: '#swagger-ui',
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl
          ],
          layout: "StandaloneLayout",
          persistAuthorization: true,
          displayRequestDuration: true,
          filter: true,
          onComplete: function() {
            console.log("Swagger UI loaded successfully");
          },
          responseInterceptor: function(response) {
            // Log any issues with API responses for debugging
            if (response.status >= 400) {
              console.error("API Response Error:", response);
            }
            return response;
          },
          requestInterceptor: function(request) {
            // Log outgoing requests for debugging
            console.log("API Request:", request);
            return request;
          }
        });
      } catch (e) {
        console.error("Error initializing Swagger UI:", e);
        errorContainer.innerHTML = `<h3>Error Loading Swagger UI</h3><p>${e.message || 'Unknown error'}</p>`;
        errorContainer.style.display = 'block';
        document.getElementById('swagger-ui').style.display = 'none';
      }
    };
  </script>
</body>
</html>
