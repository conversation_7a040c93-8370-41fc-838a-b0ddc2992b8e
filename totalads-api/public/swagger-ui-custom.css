/* Custom styles for Swagger UI */
html {
  box-sizing: border-box;
  overflow: -moz-scrollbars-vertical;
  overflow-y: scroll;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  margin: 0;
  background: #fafafa;
}

.swagger-ui .topbar {
  background-color: #1b1b1b;
}

.swagger-ui .info .title {
  font-size: 2.5em;
  margin: 20px 0;
}

.swagger-ui .opblock-tag {
  font-size: 1.2em;
}

.swagger-ui .opblock .opblock-summary-operation-id, 
.swagger-ui .opblock .opblock-summary-path, 
.swagger-ui .opblock .opblock-summary-path__deprecated {
  font-size: 14px;
}
