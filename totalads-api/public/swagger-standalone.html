<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>TotalAds API Documentation</title>
  <!-- Load Swagger UI directly from CDN with fixed version -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@3.52.5/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin: 0;
      background: #fafafa;
    }
    .swagger-ui .topbar { display: none; }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  
  <!-- Load Swagger UI scripts from CDN -->
  <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
  
  <script>
    // Fetch Swagger JSON data directly
    fetch('/api-docs/json')
      .then(response => response.json())
      .then(spec => {
        // Initialize Swagger UI with fetched spec
        window.ui = SwaggerUIBundle({
          spec: spec,
          dom_id: '#swagger-ui',
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset
          ],
          layout: "StandaloneLayout",
          defaultModelsExpandDepth: -1, // Hide schemas section by default
          displayRequestDuration: true
        });
      })
      .catch(error => {
        console.error("Error fetching Swagger spec:", error);
        document.getElementById('swagger-ui').innerHTML = `
          <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
            <h1>Error Loading API Documentation</h1>
            <p>Could not load the Swagger specification. Please check the server logs.</p>
            <p>Error details: ${error.message || 'Unknown error'}</p>
          </div>
        `;
      });
  </script>
</body>
</html>
